#!/bin/bash

# Start Development Environment
echo "🚀 Starting SaaS User Auth - Development Environment"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration"
    exit 1
fi

# Start database services first
echo "🗄️  Starting database services..."
cd 4-database-service
docker-compose up -d
cd ..

# Wait for databases to be ready
echo "⏳ Waiting for databases to be ready..."
sleep 10

# Start development services
echo "🔧 Starting development services..."
docker-compose -f docker-compose.dev.yml up -d

echo "✅ Development environment started!"
echo ""
echo "📊 Services:"
echo "   Frontend:  http://localhost:3000"
echo "   Backend:   http://localhost:3001"
echo "   GraphQL:   http://localhost:3001/graphql"
echo "   Database:  localhost:5432"
echo "   Redis:     localhost:6379"
echo ""
echo "🔍 Logs:"
echo "   docker-compose -f docker-compose.dev.yml logs -f"
echo ""
echo "🛑 Stop:"
echo "   ./scripts/stop-dev.sh"
