version: '3.8'

services:
  # Development Frontend (with hot reload)
  frontend-dev:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile.dev
    container_name: saas-frontend-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    volumes:
      - ./apps/frontend:/app/apps/frontend
      - ./packages:/app/packages
      - /app/apps/frontend/node_modules
      - /app/apps/frontend/.next
    depends_on:
      - backend-dev
    networks:
      - dev-network
    restart: unless-stopped

  # Development Backend (with hot reload)
  backend-dev:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile.dev
    container_name: saas-backend-dev
    ports:
      - "3001:3001"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres-dev:5432/${POSTGRES_DB}
      - REDIS_URL=redis://redis-dev:6379
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - FACEBOOK_CLIENT_ID=${FACEBOOK_CLIENT_ID}
      - FACEBOOK_CLIENT_SECRET=${FACEBOOK_CLIENT_SECRET}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    volumes:
      - ./apps/backend:/app/apps/backend
      - ./packages:/app/packages
      - /app/apps/backend/node_modules
      - /app/apps/backend/dist
    depends_on:
      postgres-dev:
        condition: service_healthy
      redis-dev:
        condition: service_healthy
    networks:
      - dev-network
    restart: unless-stopped

  # Development PostgreSQL
  postgres-dev:
    image: postgres:15-alpine
    container_name: saas-postgres-dev
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Development Redis
  redis-dev:
    image: redis:7-alpine
    container_name: saas-redis-dev
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    networks:
      - dev-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Prisma Studio (for database management)
  prisma-studio:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile.dev
    container_name: saas-prisma-studio
    ports:
      - "5555:5555"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres-dev:5432/${POSTGRES_DB}
    command: ["yarn", "workspace", "@saas-userauth/backend", "db:studio", "--hostname", "0.0.0.0"]
    volumes:
      - ./apps/backend:/app/apps/backend
      - ./packages:/app/packages
    depends_on:
      postgres-dev:
        condition: service_healthy
    networks:
      - dev-network
    restart: unless-stopped
    profiles:
      - tools

networks:
  dev-network:
    driver: bridge

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
