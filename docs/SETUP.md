# Setup Guide

## Prerequisites

- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Yarn 1.22+ (Package Manager)

## Environment Setup

1. Copy environment file:
```bash
cp .env.example .env
```

2. Update the `.env` file with your configuration:
   - Database connection string
   - JWT secret
   - OAuth credentials (Google, Facebook)
   - Stripe keys
   - Email configuration

## Database Setup

1. Create PostgreSQL database:
```sql
CREATE DATABASE saas_userauth;
```

2. Generate Prisma client:
```bash
yarn db:generate
```

3. Push database schema:
```bash
yarn db:push
```

4. (Optional) Seed database:
```bash
yarn db:seed
```

## Installation

1. Install dependencies:
```bash
yarn install
```

2. Build packages:
```bash
yarn build
```

## Development

Start both frontend and backend:
```bash
yarn dev
```

Or start individually:
```bash
# Frontend only
yarn dev:frontend

# Backend only
yarn dev:backend
```

## URLs

- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- GraphQL Playground: http://localhost:3001/graphql
- Prisma Studio: http://localhost:5555 (run `yarn db:studio`)

## Yarn Workspace Commands

```bash
# Install dependencies for all workspaces
yarn install

# Run commands in specific workspace
yarn workspace @saas-userauth/frontend dev
yarn workspace @saas-userauth/backend dev

# Run commands in all workspaces
yarn workspaces run build
yarn workspaces run test

# Database commands
yarn db:generate    # Generate Prisma client
yarn db:push        # Push schema to database
yarn db:migrate     # Run migrations
yarn db:studio      # Open Prisma Studio

# Clean and fresh install
yarn clean          # Remove all node_modules
yarn fresh          # Clean + install
```

## OAuth Setup

### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google`
   - `http://localhost:3001/auth/google/callback`

### Facebook OAuth
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Configure OAuth redirect URIs:
   - `http://localhost:3000/api/auth/callback/facebook`
   - `http://localhost:3001/auth/facebook/callback`

## Stripe Setup

1. Create account at [Stripe](https://stripe.com)
2. Get test API keys from dashboard
3. Set up webhooks for subscription events
4. Add webhook endpoint: `http://localhost:3001/webhooks/stripe`
