# SaaS User Authentication System

Microservices-based User Authentication System with Docker

## Tech Stack

### Frontend
- **Next.js 15** - App Router
- **TypeScript** - Type safety
- **React** - UI Library
- **Tailwind CSS** - Styling
- **Shadcn/UI** - UI Components
- **GraphQL Apollo Client** - Data fetching

### Backend
- **NestJS** - Node.js framework
- **TypeScript** - Type safety
- **NestAuth + OAuth 2.0** - Authentication
- **GraphQL Apollo Server** - API
- **PostgreSQL** - Database
- **Prisma ORM** - Database ORM
- **Redis** - Caching
- **Stripe** - Payment processing
- **NGINX** - Reverse proxy

## Features

### User Types
1. **AdminUsers**
   - SuperUser (created once, creates other admin users)
   - Admin
   - Moderator

2. **WebUsers**
   - DevCompany
   - Agency
   - StandardUser (default)

### Authentication
- Local strategy (username + password / mobile + password)
- OAuth (Gmail, Facebook)
- Forgot/Reset Password functionality

### Subscriptions
- Free
- Starter
- Pro

### Payment Integration
- Stripe
- Google Pay

## 🏗️ Clean Microservices Architecture

```
saas-userauth1/
├── frontend-service/      # 🎨 Standalone Next.js 15 Service
├── backend-service/       # ⚙️  Standalone NestJS API Service
├── nginx-service/         # 🔄 Standalone NGINX Proxy Service
├── database-service/      # 🗄️  Standalone Database Service (PostgreSQL + Redis)
├── scripts/               # 🚀 Deployment & management scripts
├── docs/                  # 📚 Documentation
├── docker-compose.yml     # 🐳 Production orchestration
├── docker-compose.dev.yml # 🔧 Development orchestration
└── .env.example           # ⚙️  Environment template
```

### ✨ True Microservices Benefits:
- **🔒 Complete Independence** - Each service has its own dependencies
- **🐳 Docker Native** - Every service runs in its own container
- **📈 Individual Scaling** - Scale services independently
- **🔧 Technology Freedom** - Use different tech stacks per service
- **🚀 Fast Development** - Work on services separately

## Quick Start

### Development Environment
```bash
# 1. Copy environment file
cp .env.example .env

# 2. Edit .env with your configuration
# 3. Start development environment
./scripts/start-dev.sh

# 4. Access services
# Frontend:  http://localhost:3000
# Backend:   http://localhost:3001/graphql
# Database:  localhost:5432
```

### Production Environment
```bash
# 1. Ensure .env is configured for production
# 2. Start production environment
./scripts/start-prod.sh

# 3. Access application
# Application: http://localhost
```

## Service Management

### Individual Services
```bash
# Frontend service only
cd frontend-service && docker build -t saas-frontend .

# Backend service only
cd backend-service && docker build -t saas-backend .

# Database services only
cd database-service && docker-compose up
```

### Development Commands
```bash
# Start development
./scripts/start-dev.sh

# Stop development
./scripts/stop-dev.sh

# View logs
docker-compose -f docker-compose.dev.yml logs -f
```
