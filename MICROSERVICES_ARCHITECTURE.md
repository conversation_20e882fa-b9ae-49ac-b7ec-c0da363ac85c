# Microservices Architecture

## Overview
This project uses a microservices architecture with Docker containers for each service:

```
┌─────────────────────────────────────────────────────────────┐
│                        NGINX Proxy                         │
│                    (Load Balancer)                         │
│                     Port: 80/443                           │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  Frontend   │ │   Backend   │ │   Backend   │
│  (Next.js)  │ │  (NestJS)   │ │  (NestJS)   │
│  Port: 3000 │ │  Port: 3001 │ │  Port: 3002 │
└─────────────┘ └─────────────┘ └─────────────┘
                      │             │
                      └─────┬───────┘
                            │
        ┌───────────────────┼───────────────────┐
        │                   │                   │
        ▼                   ▼                   ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ PostgreSQL  │ │    Redis    │ │   Volumes   │
│ Port: 5432  │ │ Port: 6379  │ │   (Data)    │
└─────────────┘ └─────────────┘ └─────────────┘
```

## Services

### 1. NGINX Proxy (nginx-proxy)
- **Purpose**: Load balancer, SSL termination, static file serving
- **Port**: 80 (HTTP), 443 (HTTPS)
- **Features**: 
  - Routes traffic to frontend/backend
  - SSL/TLS termination
  - Static file caching
  - Rate limiting

### 2. Frontend Service (frontend)
- **Technology**: Next.js 14
- **Port**: 3000
- **Features**:
  - Server-side rendering
  - Static file generation
  - API communication with backend

### 3. Backend Service (backend)
- **Technology**: NestJS
- **Port**: 3001 (primary), 3002 (secondary for scaling)
- **Features**:
  - GraphQL API
  - Authentication & Authorization
  - Business logic
  - Database operations

### 4. Database Service (postgres)
- **Technology**: PostgreSQL 15
- **Port**: 5432
- **Features**:
  - Primary data storage
  - ACID compliance
  - Backup & recovery

### 5. Cache Service (redis)
- **Technology**: Redis 7
- **Port**: 6379
- **Features**:
  - Session storage
  - Caching layer
  - Rate limiting data

## Network Architecture

### Docker Networks
- **frontend-network**: Frontend ↔ NGINX
- **backend-network**: Backend ↔ Database/Redis
- **proxy-network**: NGINX ↔ All services

### Service Communication
- Frontend → Backend: HTTP/GraphQL (via NGINX)
- Backend → PostgreSQL: TCP connection
- Backend → Redis: TCP connection
- NGINX → Frontend/Backend: HTTP proxy

## Scaling Strategy

### Horizontal Scaling
- Multiple backend instances behind NGINX
- Database read replicas (future)
- Redis cluster (future)

### Load Balancing
- NGINX round-robin for backend services
- Health checks for service availability
- Automatic failover

## Security

### Network Security
- Internal Docker networks
- No direct external access to databases
- NGINX as single entry point

### Data Security
- Environment-based secrets
- SSL/TLS encryption
- Database connection encryption

## Development vs Production

### Development
- Hot reload for frontend/backend
- Database with sample data
- Debug logging enabled
- Development SSL certificates

### Production
- Optimized builds
- Production database
- Minimal logging
- Let's Encrypt SSL certificates
- Health monitoring
- Backup strategies
