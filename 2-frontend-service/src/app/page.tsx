'use client'

import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function HomePage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <header className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4">
            SaaS User Auth
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Advanced authentication with secure route groups
          </p>
        </header>
        {/* Main Action Cards */}
        <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8 mb-16">
          {/* Standard User Access */}
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-2xl text-blue-600">Standard Access</CardTitle>
              <CardDescription>
                For regular users, developers, and agencies
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2 text-sm text-gray-600">
                <div>✓ Standard Accounts</div>
                <div>✓ DevCompany Access</div>
                <div>✓ Agency Management</div>
                <div>✓ Full Dashboard</div>
              </div>
              <div className="space-y-2">
                <Button
                  size="lg"
                  className="w-full"
                  onClick={() => router.push('/auth/(webuser)/signup')}
                >
                  Create Account
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push('/auth/(webuser)/signin')}
                >
                  Sign In
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Admin Access */}
          <Card className="text-center hover:shadow-lg transition-shadow border-red-200">
            <CardHeader>
              <CardTitle className="text-2xl text-red-600">Administrator Access</CardTitle>
              <CardDescription>
                For system administrators only
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2 text-sm text-gray-600">
                <div>✓ User Management</div>
                <div>✓ System Control</div>
                <div>✓ Admin Dashboard</div>
                <div>✓ Full Permissions</div>
              </div>
              <div className="space-y-2">
                <Button
                  size="lg"
                  variant="destructive"
                  className="w-full"
                  onClick={() => router.push('/auth/(adminuser)/signin')}
                >
                  Admin Login
                </Button>
                <p className="text-xs text-red-600">
                  Admin accounts cannot be self-registered
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gray-100 dark:bg-gray-800">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-3 lg:gap-12">
              <div className="flex flex-col justify-center space-y-4">
                <h2 className="text-2xl font-bold">Multiple User Roles</h2>
                <p className="text-gray-500 dark:text-gray-400">
                  Support for different user roles including Admin, DevCompany, Agency, and Standard users.
                </p>
              </div>
              <div className="flex flex-col justify-center space-y-4">
                <h2 className="text-2xl font-bold">OAuth Integration</h2>
                <p className="text-gray-500 dark:text-gray-400">
                  Sign in with Google and Facebook, plus traditional email/password authentication.
                </p>
              </div>
              <div className="flex flex-col justify-center space-y-4">
                <h2 className="text-2xl font-bold">Subscription Plans</h2>
                <p className="text-gray-500 dark:text-gray-400">
                  Choose from Free, Starter, and Pro plans with different feature sets and limits.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>
      <footer className="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center px-4 md:px-6 border-t">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          © 2024 SaaS User Auth. All rights reserved.
        </p>
        <nav className="flex gap-4 sm:ml-auto sm:gap-6">
          <Link className="text-xs hover:underline underline-offset-4" href="#">
            Terms of Service
          </Link>
          <Link className="text-xs hover:underline underline-offset-4" href="#">
            Privacy
          </Link>
        </nav>
      </footer>
    </div>
  )
}
