"use client"

import { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { gql, useApolloClient } from '@apollo/client'
import { type AuthResponse } from '@/types'

interface AuthContextType {
  user: AuthResponse['user'] | null
  loading: boolean
  signIn: (token: string, user: AuthResponse['user'], callbackUrl?: string) => void
  signOut: () => void
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signIn: () => {},
  signOut: () => {},
})

const ME_QUERY = gql`
  query Me {
    me {
      id
      email
      name
      role
    }
  }
`

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthResponse['user'] | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const client = useApolloClient()

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      client.query({
        query: ME_QUERY,
      }).then(({ data }) => {
        if (data?.me) {
          setUser(data.me)
        }
      }).catch(() => {
        // Clear both localStorage and cookie on error
        localStorage.removeItem('token')
        document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
      }).finally(() => {
        setLoading(false)
      })
    } else {
      setLoading(false)
    }
  }, [client])

  const signIn = (token: string, user: AuthResponse['user'], callbackUrl: string = '/dashboard') => {
    // Store token in localStorage for Apollo Client
    localStorage.setItem('token', token)

    // Store token in cookie for middleware
    document.cookie = `token=${token}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`

    setUser(user)

    // Redirect to callback URL or appropriate dashboard
    if (callbackUrl && callbackUrl !== '/dashboard') {
      router.push(callbackUrl)
    } else {
      // Redirect based on user role
      if (user.role === 'ADMINUSER') {
        router.push('/adashboard')
      } else {
        router.push('/wdashboard')
      }
    }
  }

  const signOut = () => {
    // Clear localStorage
    localStorage.removeItem('token')

    // Clear cookie
    document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'

    setUser(null)
    client.resetStore()
    router.push('/wuser/signin')
  }

  return (
    <AuthContext.Provider value={{ user, loading, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
