# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type AuthPayload {
  token: String!
  user: UserType!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

input ForgotPasswordInput {
  email: String!
}

type MessageResponse {
  message: String!
}

type Mutation {
  forgotPassword(input: ForgotPasswordInput!): MessageResponse!
  resetPassword(input: ResetPasswordInput!): MessageResponse!
  signIn(input: SignInInput!): AuthPayload!
  signUp(input: SignUpInput!): AuthPayload!
}

type Query {
  me: UserType!
}

input ResetPasswordInput {
  password: String!
  token: String!
}

input SignInInput {
  email: String!
  password: String!
}

input SignUpInput {
  email: String!
  name: String
  password: String!
}

type UserType {
  createdAt: DateTime!
  email: String!
  id: String!
  name: String
  role: String!
  updatedAt: DateTime!
}