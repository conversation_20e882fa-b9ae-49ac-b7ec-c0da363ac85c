{"name": "saas-<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "SaaS User Authentication System with Next.js 15 and NestJS", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "npm run dev --workspace=apps/frontend", "dev:backend": "npm run dev --workspace=apps/backend", "build": "npm run build --workspaces", "build:frontend": "npm run build --workspace=apps/frontend", "build:backend": "npm run build --workspace=apps/backend", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "npm run start --workspace=apps/frontend", "start:backend": "npm run start --workspace=apps/backend", "lint": "npm run lint --workspaces", "test": "npm run test --workspaces", "db:generate": "npm run db:generate --workspace=apps/backend", "db:push": "npm run db:push --workspace=apps/backend", "db:migrate": "npm run db:migrate --workspace=apps/backend", "db:studio": "npm run db:studio --workspace=apps/backend"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}