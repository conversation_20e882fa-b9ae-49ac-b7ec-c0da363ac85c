# 📋 Development Plan for SaaS User Authentication System (Microservices Architecture)

## 0. Project Structure (Following NUMBERED_STRUCTURE.md)

```
saas-userauth1/
├── 1-nginx-service/           # Load Balancer & Reverse Proxy
│   ├── conf.d/               
│   │   └── default.conf      # Service routing & SSL config
│   └── nginx.conf            # Main NGINX configuration
│
├── 2-frontend-service/        # Next.js 15 Frontend
│   ├── src/
│   │   ├── app/             # App Router Structure
│   │   │   ├── auth/        # Authentication Routes
│   │   │   │   ├── signin/
│   │   │   │   ├── signup/
│   │   │   │   ├── forgot-password/
│   │   │   │   └── reset-password/
│   │   │   ├── dashboard/   # Protected Routes
│   │   │   │   ├── admin/   # Admin Dashboard
│   │   │   │   └── user/    # User Dashboard
│   │   │   └── page.tsx     # Landing Page
│   │   ├── components/      # React Components
│   │   │   ├── auth/       # Auth Components
│   │   │   ├── dashboard/  # Dashboard Components
│   │   │   └── ui/        # Shadcn/UI Components
│   │   ├── graphql/       # Apollo Client Setup
│   │   └── lib/          # Utilities & Providers
│
├── 3-backend-service/         # NestJS Backend
│   ├── src/
│   │   ├── auth/            # Auth Module
│   │   ├── users/           # Users Module
│   │   ├── subscription/    # Subscription Module
│   │   └── graphql/        # GraphQL Definitions
│   └── prisma/             # Database Schema
│
└── 4-database-service/        # Data Layer
    ├── init/                # DB Initialization
    └── redis.conf          # Redis Configuration

## 1. Database Schema (4-database-service/init/)

### Prisma Schema (3-backend-service/prisma/schema.prisma)
```prisma
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  username      String?   @unique
  phoneNumber   String?   @unique
  password      String?   // Hashed password, nullable for OAuth users
  firstName     String?
  lastName      String?
  profileImage  String?
  
  // Auth Related
  emailVerified    DateTime?
  phoneVerified    DateTime?
  authProvider     AuthProvider @default(LOCAL)
  authProviderId   String?     // OAuth provider ID
  
  // Role Management
  role             UserRole    @default(STANDARD_USER)
  adminRole        AdminRole?  // Only for admin users
  
  // Subscription
  subscriptionTier SubscriptionTier @default(FREE)
  stripeCustomerId String?
  subscriptions    Subscription[]
  
  // Timestamps
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Relations
  resetTokens   PasswordReset[]
  sessions      Session[]
}

enum AuthProvider {
  LOCAL
  GOOGLE
  FACEBOOK
}

enum UserRole {
  ADMIN_USER
  WEB_USER
}

enum AdminRole {
  SUPER_USER
  ADMIN
  MODERATOR
}

enum SubscriptionTier {
  FREE
  STARTER
  PRO
}

model PasswordReset {
  id        String    @id @default(cuid())
  token     String    @unique
  expires   DateTime
  user      User      @relation(fields: [userId], references: [id])
  userId    String
  used      Boolean   @default(false)
  createdAt DateTime  @default(now())
}

model Session {
  id        String   @id @default(cuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  token     String   @unique
  expires   DateTime
  createdAt DateTime @default(now())
}

model Subscription {
  id              String   @id @default(cuid())
  user            User     @relation(fields: [userId], references: [id])
  userId          String
  stripeSubId     String?  @unique
  tier            SubscriptionTier
  status          String
  currentPeriodEnd DateTime
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}
```

## 2. GraphQL Implementation

### Frontend Schema (2-frontend-service/src/graphql/)

### Types (3-backend-service/src/graphql/schema.graphql)
```graphql
type User {
  id: ID!
  email: String!
  username: String
  phoneNumber: String
  firstName: String
  lastName: String
  profileImage: String
  emailVerified: DateTime
  phoneVerified: DateTime
  authProvider: AuthProvider!
  role: UserRole!
  adminRole: AdminRole
  subscriptionTier: SubscriptionTier!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type AuthResponse {
  user: User!
  token: String!
}

type SubscriptionInfo {
  tier: SubscriptionTier!
  status: String!
  currentPeriodEnd: DateTime!
}
```

### Queries
```graphql
type Query {
  me: User
  userProfile(id: ID!): User
  adminUsers(skip: Int, take: Int): [User!]!
  webUsers(skip: Int, take: Int): [User!]!
  subscriptionDetails: SubscriptionInfo
}
```

### Mutations
```graphql
type Mutation {
  # Local Auth
  signUp(input: SignUpInput!): AuthResponse!
  signIn(input: SignInInput!): AuthResponse!
  signOut: Boolean!
  
  # OAuth
  googleAuth(token: String!): AuthResponse!
  facebookAuth(token: String!): AuthResponse!
  
  # Password Management
  forgotPassword(email: String!): Boolean!
  resetPassword(token: String!, newPassword: String!): Boolean!
  changePassword(oldPassword: String!, newPassword: String!): Boolean!
  
  # Profile Management
  updateProfile(input: UpdateProfileInput!): User!
  
  # Admin Operations
  createAdminUser(input: CreateAdminInput!): User!
  updateUserRole(userId: ID!, role: UserRole!): User!
  
  # Subscription Management
  createStripeCheckoutSession(tier: SubscriptionTier!): String!
  handleStripeWebhook(input: JSON!): Boolean!
}
```

## 3. Implementation Phases (Following Microservices Architecture)

### Phase 1: Core Authentication Setup

#### 1-nginx-service
- Configure SSL termination
- Set up reverse proxy rules
- Implement rate limiting

#### 2-frontend-service (Next.js 15 with App Router)
1. Next.js 15 App Router Setup
   - Implement route groups for auth flows
   - Set up server components for improved performance
   - Configure client components for interactive features
   - Auth Pages (Server Components)
     ```typescript
     // app/auth/signin/page.tsx
     export default async function SignInPage() {
       return <SignInForm />
     }
     
     // app/auth/signup/page.tsx
     export default async function SignUpPage() {
       return <SignUpForm />
     }
     ```
   - Auth Forms (Client Components)
   - Loading & Error States
   - Toast Notifications (using Shadcn/UI)
   - Dashboard layouts (Admin & Web User)
   - Protected route middleware

#### 3-backend-service
1. NestJS Authentication Module
   - Local strategy implementation
   - JWT authentication
   - Password hashing & validation
   - Session management with Redis

### Phase 2: OAuth Integration & Session Management

#### 2-frontend-service
1. Google OAuth implementation
   - Frontend OAuth flow
   - Backend OAuth handlers
   - User profile sync

2. Facebook OAuth implementation
   - Frontend OAuth flow
   - Backend OAuth handlers
   - User profile sync

### Phase 3: Role & Permission Management

#### 2-frontend-service
1. Admin user management
   - SuperUser creation flow
   - Admin user CRUD operations
   - Role-based access control

2. Web user management
   - Registration with role selection
   - Role-based dashboard routing
   - Permission management

### Phase 4: Subscription & Payment System

#### 2-frontend-service
1. Stripe integration
   - Subscription plans setup
   - Payment flow implementation
   - Webhook handlers

2. Billing UI
   - Subscription management dashboard
   - Payment history
   - Plan upgrade/downgrade flow

## 4. Security Implementation

### 1-nginx-service
- SSL/TLS configuration
- HTTP security headers
- DDoS protection
- Rate limiting rules

### 2-frontend-service
- CSRF protection
- XSS prevention
- Content Security Policy
- Secure cookie handling

### 3-backend-service

1. Authentication Security
   - Password hashing with bcrypt
   - JWT with short expiry + refresh tokens
   - Rate limiting on auth endpoints
   - CSRF protection

2. OAuth Security
   - State parameter validation
   - Strict callback URL validation
   - Token validation & verification

3. Data Protection
   - Input validation & sanitization
   - GraphQL query complexity limits
   - Request rate limiting
   - Data encryption at rest

## 5. Performance Optimization

### 1-nginx-service
- Static file caching
- Gzip compression
- HTTP/2 support
- Load balancing configuration

### 2-frontend-service
- Next.js 15 optimizations:
  - Server Components
  - Route Groups
  - Parallel Routes
  - Intercepting Routes
  - Static & Dynamic Rendering
  - Image Optimization
  - Font Optimization

### 3-backend-service

1. Caching Strategy
   - Redis caching for sessions
   - GraphQL query caching
   - Static page generation for public pages

2. Database Optimization
   - Proper indexing
   - Connection pooling
   - Query optimization

## 6. Testing Strategy

### 2-frontend-service
- Jest + React Testing Library
- Cypress for E2E testing
- Component testing
- Route testing
- Integration testing

### 3-backend-service

1. Unit Tests
   - Auth resolvers
   - GraphQL mutations
   - Service layer logic

2. Integration Tests
   - Auth flows
   - OAuth providers
   - Subscription management

3. E2E Tests
   - User journeys
   - Payment flows
   - Role-based access

## Next Steps (Following Numbered Structure)

1. Initial Setup (1 → 2 → 3 → 4)
   - Set up NGINX configuration
   - Initialize Next.js 15 frontend with App Router
   - Configure NestJS backend
   - Initialize database service

2. Core Implementation
   - Implement authentication flow
   - Set up GraphQL API
   - Configure database schema
   - Establish service communication

3. Testing & Optimization
   - Unit & integration tests
   - Performance optimization
   - Security hardening
   - Load testing

4. Review & Deployment
   - Team code review
   - Security audit
   - Performance testing
   - Staged deployment
