import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default function HomePage() {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="px-4 lg:px-6 h-14 flex items-center">
        <Link className="flex items-center justify-center" href="/">
          <span className="text-xl font-bold">SaaS User Auth</span>
        </Link>
        <nav className="ml-auto flex gap-4 sm:gap-6">
          <Link className="text-sm font-medium hover:underline underline-offset-4" href="/auth/webuser/signin">
            WebUser Sign In
          </Link>
          <Link className="text-sm font-medium hover:underline underline-offset-4" href="/auth/webuser/signup">
            WebUser Sign Up
          </Link>
        </nav>
      </header>
      <main className="flex-1">
        <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                  Welcome to SaaS User Auth
                </h1>
                <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
                  A complete authentication solution with role-based access control and subscription management.
                </p>
              </div>
              <div className="space-x-4">
                <Link href="/auth/webuser/signup">
                  <Button>Get Started as WebUser</Button>
                </Link>
                <Link href="/auth/webuser/signin">
                  <Button variant="outline">WebUser Sign In</Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gray-100 dark:bg-gray-800">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-3 lg:gap-12">
              <div className="flex flex-col justify-center space-y-4">
                <h2 className="text-2xl font-bold">Multiple User Roles</h2>
                <p className="text-gray-500 dark:text-gray-400">
                  Support for different user roles including Admin, DevCompany, Agency, and Standard users.
                </p>
              </div>
              <div className="flex flex-col justify-center space-y-4">
                <h2 className="text-2xl font-bold">OAuth Integration</h2>
                <p className="text-gray-500 dark:text-gray-400">
                  Sign in with Google and Facebook, plus traditional email/password authentication.
                </p>
              </div>
              <div className="flex flex-col justify-center space-y-4">
                <h2 className="text-2xl font-bold">Subscription Plans</h2>
                <p className="text-gray-500 dark:text-gray-400">
                  Choose from Free, Starter, and Pro plans with different feature sets and limits.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>
      <footer className="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center px-4 md:px-6 border-t">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          © 2024 SaaS User Auth. All rights reserved.
        </p>
        <nav className="flex gap-4 sm:ml-auto sm:gap-6">
          <Link className="text-xs hover:underline underline-offset-4" href="#">
            Terms of Service
          </Link>
          <Link className="text-xs hover:underline underline-offset-4" href="#">
            Privacy
          </Link>
        </nav>
      </footer>
    </div>
  )
}
