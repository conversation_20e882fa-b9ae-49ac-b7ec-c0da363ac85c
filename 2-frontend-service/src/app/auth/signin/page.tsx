import { Metadata } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { SignInForm } from '@/components/auth/signin-form'

export const metadata: Metadata = {
  title: 'Choose Login Type - SaaS User Auth',
  description: 'Choose your account type to sign in',
}

export default function SignInPage() {
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className="absolute inset-0 bg-zinc-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <Link href="/">SaaS User Auth</Link>
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              &ldquo;This authentication system has streamlined our user management process and made our application more secure.&rdquo;
            </p>
            <footer className="text-sm">Sofia Davis</footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
          <Card>
            <CardHeader>
              <CardTitle>Choose Your Login Type</CardTitle>
              <CardDescription>
                Select your account type to access the appropriate login page
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Link href="/auth/webuser/signin">
                <Button className="w-full h-16 bg-blue-600 hover:bg-blue-700" size="lg">
                  <div className="text-center">
                    <div className="font-semibold text-lg">WebUser Login</div>
                    <div className="text-sm opacity-90">Standard • DevCompany • Agency</div>
                  </div>
                </Button>
              </Link>

              <Link href="/auth/adminuser/signin">
                <Button className="w-full h-16 bg-red-600 hover:bg-red-700" size="lg">
                  <div className="text-center">
                    <div className="font-semibold text-lg">AdminUser Login</div>
                    <div className="text-sm opacity-90">Administrative Access</div>
                  </div>
                </Button>
              </Link>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <div className="text-center text-sm text-muted-foreground">
                Don&apos;t have a WebUser account?{' '}
                <Link
                  href="/auth/signup"
                  className="underline underline-offset-4 hover:text-primary text-blue-600"
                >
                  Sign up as WebUser
                </Link>
              </div>
              <div className="text-center text-xs text-muted-foreground bg-gray-50 p-2 rounded">
                <strong>Note:</strong> AdminUser accounts cannot be self-registered and must be created by existing AdminUsers.
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
