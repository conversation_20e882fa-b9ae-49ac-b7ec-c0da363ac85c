export interface User {
  id: string;
  email: string;
  name: string;
  role: 'WEB<PERSON><PERSON>' | 'ADMINUSER';
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface SignInInput {
  email: string;
  password: string;
}

export interface SignUpInput {
  email: string;
  password: string;
  name: string;
}

export interface ForgotPasswordInput {
  email: string;
}

export interface ResetPasswordInput {
  token: string;
  password: string;
}

export interface SignInMutation {
  signIn: AuthResponse;
}

export interface SignUpMutation {
  signUp: AuthResponse;
}

export interface ForgotPasswordMutation {
  forgotPassword: boolean;
}

export interface ResetPasswordMutation {
  resetPassword: boolean;
}

export interface MeQuery {
  me: User;
}
