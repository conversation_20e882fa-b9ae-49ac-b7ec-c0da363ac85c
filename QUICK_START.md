# 🚀 Quick Start Guide

## Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Yarn 1.22+

## 1. Installation
```bash
# Clone and navigate to project
cd saas-userauth1

# Install all dependencies
yarn install
```

## 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration:
# - Database connection string
# - JWT secret
# - OAuth credentials (Google, Facebook)
# - Stripe keys
```

## 3. Database Setup
```bash
# Generate Prisma client
yarn db:generate

# Push schema to database
yarn db:push

# (Optional) Seed database
yarn db:seed
```

## 4. Start Development
```bash
# Start both frontend and backend
yarn dev

# Or start individually:
yarn dev:frontend  # Frontend only (port 3000)
yarn dev:backend   # Backend only (port 3001)
```

## 5. Access Applications
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **GraphQL Playground**: http://localhost:3001/graphql
- **Prisma Studio**: `yarn db:studio` → http://localhost:5555

## Common Commands
```bash
# Development
yarn dev                    # Start both apps
yarn dev:frontend          # Start frontend only
yarn dev:backend           # Start backend only

# Building
yarn build                 # Build all packages
yarn build:frontend        # Build frontend only
yarn build:backend         # Build backend only

# Database
yarn db:generate           # Generate Prisma client
yarn db:push              # Push schema to DB
yarn db:migrate           # Run migrations
yarn db:studio            # Open Prisma Studio

# Maintenance
yarn clean                 # Clean all node_modules
yarn fresh                 # Clean + fresh install
```

## Workspace Commands
```bash
# Add dependency to specific workspace
yarn workspace @saas-userauth/frontend add package-name
yarn workspace @saas-userauth/backend add package-name

# Run command in specific workspace
yarn workspace @saas-userauth/frontend lint
yarn workspace @saas-userauth/backend test
```

## Next Steps
1. Configure OAuth providers (Google, Facebook)
2. Set up Stripe for payments
3. Configure email service for password reset
4. Review and customize the authentication flow

## Documentation
- [Setup Guide](docs/SETUP.md) - Detailed setup instructions
- [Yarn Guide](docs/YARN_GUIDE.md) - Yarn workspace management
- [Project Structure](docs/PROJECT_STRUCTURE.md) - Code organization

## Troubleshooting
- **Dependencies issues**: `yarn fresh`
- **Database issues**: `yarn db:generate`
- **TypeScript errors**: `yarn workspace <name> type-check`

Happy coding! 🎉
