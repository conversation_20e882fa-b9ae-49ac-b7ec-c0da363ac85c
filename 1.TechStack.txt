Full Stack Developement / Saas - User Auth.

ძირითადათ ვმუშაობ e-comerce <PERSON> როგორებიცაა გაყიდვების პლატფორმა და უძრავი ქონების ლისთინგის ვებსაიტი. 

ვიყენებ Node.js, ჩემი ტექ-სტექია Frontend: Nextjs V15- APP Router, Typescript , React, Tailwind css, Shadcn/UI, Grafql- Apolo Client. 

Backend: Nest.js, TypeScript, Nest Auth + OAuth 2.0,  Grafql-Apolo Server, DB - Posgresql, Prisma ORM,  Cache-Redis, Payment-Stripe, NGINX-Reverse Prox.

გთხოვ დამეხმარე  შევქმნა  User Autentification Local strategy-(username+pass / mobile number + pass ) +  Auth with gmail and Facebook. 

გვექნება 2 ტიპის იუზერი: 1. AdminUsers ( Role: SuperUser, Admin, Moderator), SuperUser შეიქმნება ერთხელ და იგი შექმნის დანარჩენ AdminUsers-ებს, 2. WebUsers ( Role: DevCompany, Agency, StandardUser), მომხმარებლები ვალდებულნი არიან დარეგისტრირდნენ Sign up გვერდიდან სადაც წინასწარ დატანილი იქნება სავალდებულო ასარჩევი Role ამისთვის განთავსებული იქნება წინასწარ გამზადებული როლის ასარჩევი ღილაკები DevCompany და Agency ხოლო არა არჩევის შემთხვევაში დიფოლტად მოხდება StandardUser რეგისტრაცია.

წარმატებით დარეგისტრირების შემთხვევაში  Sign up გვერდის ბოლოში გამოჩნდება შეტყობინება “რეგისტრაცია წარმატებულია, გადამისამართება Log in page”.

წარმატებით დალოგინების შემთხვევაში Log in page-ს ბოლოში გამოჩნდება შეტყობინება “აუთენტიფიკაცია წარმატებულია,გადამისამართება Dash Board-ზე”( იგულისხმება  WebUsers DashBoard ან AdminUsers Dashboard). User-ის ტიპის განსაზღვრა ხდება ავტომატურად სისტემის მიერ,შესაბამისადაც გვაქვს 2 ტიპის დაშბორდი: WebUsers DashBoard ან AdminUsers Dashboard.

 გვჭირდება Forgot / Reset Password გვერდი და ლოგიკა და ფუნქციონალი.

ასევე მნიშვნელოვანია გადახდის ლოგიკა და ფუნქციონალი რამდენადაც ჩვენ გვექნება Subscription: Free, Starter, Pro.პირველ ეტაპზე გადახდის ფორმა იქნება Stripe და Google Pay. 

Subscribed WebUsers დაშბორდში დაემატებათ Billing-თან დაკავშირებული UI და ფუნქციონალი

// გთხოვ პირველ რიგში გავაკეთოთ მონახაზი საუკეთესო პრაქტიკის გამოყენებით და ავირჩიოთ ვარიანტები. მნიშვნელოვანია ასევე Grafql Query-ს და Grafql Mutation-ების წინასწარ განსაზღვრა და ასევე შესაბამისი Resolvers შექმნა



გვჭირდება Open Source და უფასო გადაწყვეტილებებზე ფოკუსირება, მესამე მხარის სერვისების მინიმიზაციით. ასევე, Google-ისა და Facebook-ის OAuth API-დან მიღებული ყველა მონაცემი (როგორიცაა email, სახელი, profile picture, თუ ხელმისაწვდომია) შევინახავთ ჩვენს PostgreSQL ბაზაში.მონახაზს, რათა დავამატოთ და შევქმნათ არტიფაქტი, რომელიც მოიცავს Prisma Schema-ს, GraphQL API-ს და Backend ლოგიკის ნაწილს, ასევე შევქმნათ სტრუქტურის ხე თუ როგორ შეიძლება გამოიყურებოდეს პროექტის სტრუქტურა ამ კონკრეტული მიზნისთვის-User Auth.

Forgot Password და Reset Password ფუნქციონალი,

მადლობა, მიყვარხარ ♥️