export enum UserType {
  ADMIN_USER = 'ADMIN_USER',
  WEB_USER = 'WEB_USER',
}

export enum AdminRole {
  SUPER_USER = 'SUPER_USER',
  ADMIN = 'ADMIN',
  MODERATOR = 'MODERATOR',
}

export enum WebUserRole {
  DEV_COMPANY = 'DEV_COMPANY',
  AGENCY = 'AGENCY',
  STANDARD_USER = 'STANDARD_USER',
}

export enum SubscriptionPlan {
  FREE = 'FREE',
  STARTER = 'STARTER',
  PRO = 'PRO',
}

export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  CANCELLED = 'CANCELLED',
  PAST_DUE = 'PAST_DUE',
}

export enum AuthProvider {
  LOCAL = 'LOCAL',
  GOOGLE = 'GOOGLE',
  FACEBOOK = 'FACEBOOK',
}

export interface User {
  id: string
  email: string
  username?: string
  mobile?: string
  firstName?: string
  lastName?: string
  avatar?: string
  userType: UserType
  adminRole?: AdminRole
  webUserRole?: WebUserRole
  isEmailVerified: boolean
  isMobileVerified: boolean
  isActive: boolean
  authProviders: AuthProvider[]
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date
}

export interface Subscription {
  id: string
  userId: string
  plan: SubscriptionPlan
  status: SubscriptionStatus
  currentPeriodStart?: Date
  currentPeriodEnd?: Date
  cancelAtPeriodEnd: boolean
  createdAt: Date
  updatedAt: Date
}
