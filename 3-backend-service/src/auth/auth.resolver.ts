import { Args, Mutation, Query, Resolver, ObjectType, Field } from '@nestjs/graphql';
import { AuthService } from './auth.service';
import { UseGuards } from '@nestjs/common';
import { GqlAuthGuard } from './guards/gql-auth.guard';
import { CurrentUser } from './decorators/current-user.decorator';
import { User } from '@prisma/client';
import { SignInInput, SignUpInput, ForgotPasswordInput, ResetPasswordInput } from './dto/auth.dto';

@ObjectType()
export class AuthPayload {
  @Field()
  token: string;

  @Field(() => UserType)
  user: User;
}

@ObjectType()
export class UserType {
  @Field()
  id: string;

  @Field()
  email: string;

  @Field({ nullable: true })
  name?: string;

  @Field()
  role: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class MessageResponse {
  @Field()
  message: string;
}

@Resolver()
export class AuthResolver {
  constructor(private readonly authService: AuthService) {}

  @Mutation(() => AuthPayload)
  async signIn(@Args('input') input: SignInInput) {
    return this.authService.signIn(input);
  }

  @Mutation(() => AuthPayload)
  async signUp(@Args('input') input: SignUpInput) {
    return this.authService.signUp(input);
  }

  @Mutation(() => MessageResponse)
  async forgotPassword(@Args('input') input: ForgotPasswordInput) {
    await this.authService.forgotPassword(input.email);
    return { message: 'Password reset email sent' };
  }

  @Mutation(() => MessageResponse)
  async resetPassword(@Args('input') input: ResetPasswordInput) {
    await this.authService.resetPassword(input.token, input.password);
    return { message: 'Password reset successful' };
  }

  @Query(() => UserType)
  @UseGuards(GqlAuthGuard)
  async me(@CurrentUser() user: User) {
    return user;
  }
}
