# Setup Guide

## Prerequisites

- Node.js 18+ 
- PostgreSQL 14+
- Redis 6+
- npm or yarn

## Environment Setup

1. Copy environment file:
```bash
cp .env.example .env
```

2. Update the `.env` file with your configuration:
   - Database connection string
   - JWT secret
   - OAuth credentials (Google, Facebook)
   - Stripe keys
   - Email configuration

## Database Setup

1. Create PostgreSQL database:
```sql
CREATE DATABASE saas_userauth;
```

2. Generate Prisma client:
```bash
npm run db:generate
```

3. Push database schema:
```bash
npm run db:push
```

4. (Optional) Seed database:
```bash
npm run db:seed
```

## Installation

1. Install dependencies:
```bash
npm install
```

2. Build packages:
```bash
npm run build
```

## Development

Start both frontend and backend:
```bash
npm run dev
```

Or start individually:
```bash
# Frontend only
npm run dev:frontend

# Backend only  
npm run dev:backend
```

## URLs

- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- GraphQL Playground: http://localhost:3001/graphql
- Prisma Studio: http://localhost:5555 (run `npm run db:studio`)

## OAuth Setup

### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google`
   - `http://localhost:3001/auth/google/callback`

### Facebook OAuth
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Configure OAuth redirect URIs:
   - `http://localhost:3000/api/auth/callback/facebook`
   - `http://localhost:3001/auth/facebook/callback`

## Stripe Setup

1. Create account at [Stripe](https://stripe.com)
2. Get test API keys from dashboard
3. Set up webhooks for subscription events
4. Add webhook endpoint: `http://localhost:3001/webhooks/stripe`
