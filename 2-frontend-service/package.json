{"name": "saas-<PERSON><PERSON>h-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "type-check": "tsc --noEmit", "clean": "rm -rf .next", "dev:debug": "cross-env NODE_OPTIONS='--inspect' next dev", "test": "jest", "test:watch": "jest --watch", "prepare": "husky install"}, "dependencies": {"@apollo/client": "^3.13.8", "@apollo/experimental-nextjs-app-support": "^0.5.1", "@hookform/resolvers": "3.3.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "graphql": "^16.11.0", "lucide-react": "^0.525.0", "next": "14.0.3", "next-auth": "^4.24.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "7.49.2", "tailwind-merge": "^3.3.1", "zod": "3.22.4"}, "devDependencies": {"@types/next": "^9.0.0", "@types/node": "^24.0.13", "@types/postcss-flexbugs-fixes": "^5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "autoprefixer": "10.4.13", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "8.4.21", "postcss-flexbugs-fixes": "^5.0.2", "postcss-import": "^16.1.1", "postcss-preset-env": "^10.2.4", "prettier": "^3.1.0", "tailwindcss": "3.2.7", "typescript": "5.3.3"}, "packageManager": "yarn@4.0.2+sha512.4e502bea682e7d8004561f916f1da2dfbe6f718024f6aa50bf8cd86f38ea3a94a7f1bf854a9ca666dd8eafcfb8d44baaa91bf5c7876e79a7aeac952c332f0e88"}