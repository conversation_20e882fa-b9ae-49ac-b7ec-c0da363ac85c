# Backend Microservice - NestJS
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat curl
WORKDIR /app

# Install Yarn 4
RUN corepack enable && corepack prepare yarn@4.0.2 --activate

# Copy package files
COPY package.json yarn.lock .yarnrc.yml ./
COPY .yarn ./.yarn

# Install dependencies with Yarn 4
RUN yarn install --immutable

# Generate Prisma client
COPY prisma ./prisma
RUN yarn prisma generate

# Build the source code
FROM base AS builder
WORKDIR /app

# Enable Yarn 4
RUN corepack enable && corepack prepare yarn@4.0.2 --activate

# Copy all files
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/.yarn ./.yarn
COPY --from=deps /app/.yarnrc.yml ./
COPY --from=deps /app/package.json ./
COPY --from=deps /app/yarn.lock ./
COPY --from=deps /app/prisma ./prisma
COPY . .

# Build the application
ENV NODE_ENV production
RUN yarn build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

# Create app user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs

# Install production dependencies only
COPY --from=deps /app/package.json ./
COPY --from=deps /app/.yarnrc.yml ./
COPY --from=deps /app/.yarn ./.yarn
RUN corepack enable && corepack prepare yarn@4.0.2 --activate && \
    yarn workspaces focus --production

# Copy built application
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/prisma ./prisma

# Install curl for health checks
RUN apk add --no-cache curl

USER nestjs

EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

CMD ["node", "dist/main"]
