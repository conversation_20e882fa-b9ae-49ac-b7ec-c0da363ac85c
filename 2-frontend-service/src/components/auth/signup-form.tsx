'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { gql, useMutation } from '@apollo/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { 
  type SignUpFormData, 
  type SignUpMutationResponse, 
  type GraphQLError,
  type UserRole 
} from '@/types'

const signUpSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  username: z.string().min(3, 'Username must be at least 3 characters'),
  role: z.enum(['DevCompany', 'Agency', 'StandardUser'] as const),
})

type FormData = z.infer<typeof signUpSchema>

const SIGN_UP_MUTATION = gql`
  mutation SignUp($email: String!, $password: String!, $username: String!, $role: String!) {
    signUp(input: { email: $email, password: $password, username: $username, role: $role }) {
      token
      user {
        id
        email
        role
      }
    }
  }
`

export function SignUpForm() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const [signUp] = useMutation<SignUpMutationResponse>(SIGN_UP_MUTATION, {
    onCompleted: (data) => {
      setIsLoading(false)
      toast({
        title: 'Success',
        description: 'Successfully signed up',
      })
      router.push('/auth/signin')
    },
    onError: (error: GraphQLError) => {
      setIsLoading(false)
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  const form = useForm<FormData>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      email: '',
      password: '',
      username: '',
      role: 'StandardUser',
    },
  })

  async function onSubmit(data: FormData) {
    setIsLoading(true)
    try {
      await signUp({
        variables: {
          email: data.email,
          password: data.password,
          username: data.username,
          role: data.role,
        },
      })
    } catch (error) {
      // Error is handled by onError callback
    }
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          {...form.register('email')}
        />
        {form.formState.errors.email && (
          <p className="text-sm text-red-500">
            {form.formState.errors.email.message}
          </p>
        )}
      </div>
      <div className="space-y-2">
        <Label htmlFor="username">Username</Label>
        <Input
          id="username"
          type="text"
          {...form.register('username')}
        />
        {form.formState.errors.username && (
          <p className="text-sm text-red-500">
            {form.formState.errors.username.message}
          </p>
        )}
      </div>
      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <Input
          id="password"
          type="password"
          {...form.register('password')}
        />
        {form.formState.errors.password && (
          <p className="text-sm text-red-500">
            {form.formState.errors.password.message}
          </p>
        )}
      </div>
      <div className="space-y-2">
        <Label>Role</Label>
        <div className="grid grid-cols-3 gap-4">
          <Button
            type="button"
            variant={form.watch('role') === 'DevCompany' ? 'default' : 'outline'}
            className="w-full"
            onClick={() => form.setValue('role', 'DevCompany')}
          >
            DevCompany
          </Button>
          <Button
            type="button"
            variant={form.watch('role') === 'Agency' ? 'default' : 'outline'}
            className="w-full"
            onClick={() => form.setValue('role', 'Agency')}
          >
            Agency
          </Button>
          <Button
            type="button"
            variant={form.watch('role') === 'StandardUser' ? 'default' : 'outline'}
            className="w-full"
            onClick={() => form.setValue('role', 'StandardUser')}
          >
            Standard
          </Button>
        </div>
      </div>
      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading ? 'Signing up...' : 'Sign up'}
      </Button>
    </form>
  )
}
