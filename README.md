# SaaS User Authentication System

Full Stack User Authentication System with Next.js 15 and NestJS

## Tech Stack

### Frontend
- **Next.js 15** - App Router
- **TypeScript** - Type safety
- **React** - UI Library
- **Tailwind CSS** - Styling
- **Shadcn/UI** - UI Components
- **GraphQL Apollo Client** - Data fetching

### Backend
- **NestJS** - Node.js framework
- **TypeScript** - Type safety
- **NestAuth + OAuth 2.0** - Authentication
- **GraphQL Apollo Server** - API
- **PostgreSQL** - Database
- **Prisma ORM** - Database ORM
- **Redis** - Caching
- **Stripe** - Payment processing
- **NGINX** - Reverse proxy

## Features

### User Types
1. **AdminUsers**
   - SuperUser (created once, creates other admin users)
   - Admin
   - Moderator

2. **WebUsers**
   - DevCompany
   - Agency
   - StandardUser (default)

### Authentication
- Local strategy (username + password / mobile + password)
- OAuth (Gmail, Facebook)
- Forgot/Reset Password functionality

### Subscriptions
- Free
- Starter
- Pro

### Payment Integration
- Stripe
- Google Pay

## Project Structure

```
saas-userauth1/
├── apps/
│   ├── frontend/          # Next.js 15 App
│   └── backend/           # NestJS API
├── packages/
│   ├── shared/            # Shared types and utilities
│   └── ui/                # Shared UI components
├── docs/                  # Documentation
└── tools/                 # Build tools and scripts
```

## Getting Started

1. Install dependencies
2. Set up environment variables
3. Run database migrations
4. Start development servers

## Development

- Frontend: `npm run dev:frontend`
- Backend: `npm run dev:backend`
- Both: `npm run dev`
