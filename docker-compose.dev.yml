version: '3.8'

services:
  # Development Frontend (with hot reload)
  frontend-dev:
    build:
      context: ./2-frontend-service
      dockerfile: Dockerfile.dev
    container_name: saas-frontend-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    volumes:
      - ./2-frontend-service:/app
      - frontend_node_modules:/app/node_modules
      - frontend_next:/app/.next
      - frontend_yarn_cache:/app/.yarn/cache
    depends_on:
      - backend-dev
    networks:
      - dev-network
    restart: unless-stopped

  # Development Backend (with hot reload)
  backend-dev:
    build:
      context: ./3-backend-service
      dockerfile: Dockerfile.dev
    container_name: saas-backend-dev
    ports:
      - "3001:3001"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@host.docker.internal:5432/${POSTGRES_DB}
      - REDIS_URL=redis://host.docker.internal:6379
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - FACEBOOK_CLIENT_ID=${FACEBOOK_CLIENT_ID}
      - FACEBOOK_CLIENT_SECRET=${FACEBOOK_CLIENT_SECRET}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    volumes:
      - ./3-backend-service:/app
      - backend_node_modules:/app/node_modules
      - backend_dist:/app/dist
      - backend_yarn_cache:/app/.yarn/cache
    # No dependencies needed since we use external database services
    networks:
      - dev-network
    restart: unless-stopped

  # Note: Using external production database services (PostgreSQL and Redis)
  # running on host.docker.internal:5432 and host.docker.internal:6379

  # Prisma Studio (for database management)
  prisma-studio:
    build:
      context: ./3-backend-service
      dockerfile: Dockerfile.dev
    container_name: saas-prisma-studio
    ports:
      - "5555:5555"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@host.docker.internal:5432/${POSTGRES_DB}
    command: ["yarn", "db:studio", "--hostname", "0.0.0.0"]
    volumes:
      - ./3-backend-service:/app
      - backend_node_modules:/app/node_modules
    networks:
      - dev-network
    restart: unless-stopped
    profiles:
      - tools

networks:
  dev-network:
    driver: bridge

volumes:
  # Frontend volumes
  frontend_node_modules:
    driver: local
  frontend_next:
    driver: local
  frontend_yarn_cache:
    driver: local

  # Backend volumes
  backend_node_modules:
    driver: local
  backend_dist:
    driver: local
  backend_yarn_cache:
    driver: local
