export interface WebUser {
  id: string;
  email: string;
  name: string;
  role: 'WE<PERSON><PERSON><PERSON>';
  webUserRole: 'STANDARD' | 'DEVCOMPANY' | 'AGENCY';
}

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'ADMINUSER';
}

export type AppUser = WebUser | AdminUser;

export interface AuthResponse {
  token: string;
  user: User;
}

export interface SignInInput {
  email: string;
  password: string;
}

export interface SignUpInput {
  email: string;
  password: string;
  name: string;
}

export interface ForgotPasswordInput {
  email: string;
}

export interface ResetPasswordInput {
  token: string;
  password: string;
}

export interface SignInMutation {
  signIn: AuthResponse;
}

export interface SignUpMutation {
  signUp: AuthResponse;
}

export interface ForgotPasswordMutation {
  forgotPassword: boolean;
}

export interface ResetPasswordMutation {
  resetPassword: boolean;
}

export interface MeQuery {
  me: User;
}
