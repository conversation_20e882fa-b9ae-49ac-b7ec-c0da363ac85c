{"name": "saas-<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "SaaS User Authentication System with Next.js 15 and NestJS", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"yarn workspace @saas-userauth/frontend dev\" \"yarn workspace @saas-userauth/backend dev\"", "dev:frontend": "yarn workspace @saas-userauth/frontend dev", "dev:backend": "yarn workspace @saas-userauth/backend dev", "build": "yarn workspaces run build", "build:frontend": "yarn workspace @saas-userauth/frontend build", "build:backend": "yarn workspace @saas-userauth/backend build", "start": "concurrently \"yarn workspace @saas-userauth/frontend start\" \"yarn workspace @saas-userauth/backend start\"", "start:frontend": "yarn workspace @saas-userauth/frontend start", "start:backend": "yarn workspace @saas-userauth/backend start", "lint": "yarn workspaces run lint", "test": "yarn workspaces run test", "db:generate": "yarn workspace @saas-userauth/backend db:generate", "db:push": "yarn workspace @saas-userauth/backend db:push", "db:migrate": "yarn workspace @saas-userauth/backend db:migrate", "db:studio": "yarn workspace @saas-userauth/backend db:studio", "clean": "yarn workspaces run clean && rm -rf node_modules", "fresh": "yarn clean && yarn install"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "yarn": ">=1.22.0"}, "packageManager": "yarn@3.6.4"}