import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// List of public routes that don't require authentication
const publicRoutes = [
  '/signin',
  '/signup',
  '/forgot-password',
  '/reset-password',
]

// List of admin-only routes (Note: dashboard routing is handled by route groups)
const adminRoutes: string[] = []

export function middleware(request: NextRequest) {
  const token = request.cookies.get('token')?.value
  const { pathname } = request.nextUrl

  // Allow access to public routes
  if (publicRoutes.includes(pathname)) {
    // If user is already authenticated, redirect to dashboard
    if (token) {
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
    return NextResponse.next()
  }

  // Check authentication for protected routes
  if (!token && !publicRoutes.includes(pathname)) {
    const signInUrl = new URL('/signin', request.url)
    signInUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(signInUrl)
  }

  // Handle admin routes
  if (adminRoutes.includes(pathname)) {
    // You might want to decode the JWT and check the user role
    // For now, we'll just check if the admin claim exists in the cookie
    const isAdmin = request.cookies.get('isAdmin')?.value === 'true'
    if (!isAdmin) {
      return NextResponse.redirect(new URL('/dashboard/webuser', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}
