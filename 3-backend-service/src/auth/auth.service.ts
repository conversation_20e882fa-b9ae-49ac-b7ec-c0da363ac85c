import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import * as bcrypt from 'bcryptjs';
import { JwtService } from '@nestjs/jwt';
import { User, Role } from '@prisma/client';
import { SignUpInput, SignInInput } from './dto/auth.dto';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async signUp(input: SignUpInput): Promise<{ token: string; user: User }> {
    const existingUser = await this.prisma.user.findUnique({
      where: { email: input.email },
    });
    if (existingUser) {
      throw new UnauthorizedException('Email already in use');
    }
    const hashedPassword = await bcrypt.hash(input.password, 10);
    const user = await this.prisma.user.create({
      data: {
        email: input.email,
        password: hashedPassword,
        name: input.name,
        role: Role.WEBUSER, // All public registrations are WebUsers
      },
    });
    const token = this.jwtService.sign({ sub: user.id, email: user.email });
    return { token, user };
  }

  async signIn(input: SignInInput): Promise<{ token: string; user: User }> {
    const user = await this.prisma.user.findUnique({
      where: { email: input.email },
    });
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    const passwordValid = await bcrypt.compare(input.password, user.password);
    if (!passwordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }
    const token = this.jwtService.sign({ sub: user.id, email: user.email });
    return { token, user };
  }

  async forgotPassword(email: string): Promise<boolean> {
    // Implement sending reset password email with token
    return true;
  }

  async resetPassword(token: string, password: string): Promise<boolean> {
    // Implement reset password logic
    return true;
  }

  async signOut(userId: string): Promise<boolean> {
    // Implement sign out logic (e.g., invalidate token)
    return true;
  }
}
