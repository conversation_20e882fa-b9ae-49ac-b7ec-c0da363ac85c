import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { SignUpForm } from '@/components/auth/signup-form'

interface SharedSignUpPageProps {
  userType: 'wuser' | 'auser'
  metadata: Metadata
}

export function SharedSignUpPage({ userType, metadata }: SharedSignUpPageProps) {
  const isAdmin = userType === 'auser'
  
  // Admin users cannot self-register - show access denied
  if (isAdmin) {
    return (
      <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
        <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
          <div className="absolute inset-0 bg-red-900" />
          <div className="relative z-20 flex items-center text-lg font-medium">
            <Link href="/">SaaS User Auth</Link>
          </div>
          <div className="relative z-20 mt-auto">
            <blockquote className="space-y-2">
              <p className="text-lg">
                &ldquo;Admin accounts are created by existing administrators only.&rdquo;
              </p>
              <footer className="text-sm">Security Policy</footer>
            </blockquote>
          </div>
        </div>
        <div className="lg:p-8">
          <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
            <Card>
              <CardHeader>
                <CardTitle className="text-red-700">Access Restricted</CardTitle>
                <CardDescription>
                  Admin registration is not available
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                  <h3 className="font-semibold text-red-900">Admin Account Creation</h3>
                  <p className="text-sm text-red-700 mt-1">
                    Admin accounts cannot be self-registered. Only existing administrators 
                    can create new admin accounts through the admin dashboard.
                  </p>
                </div>
                <div className="text-center">
                  <Link
                    href="/auth/wuser/signin"
                    className="text-blue-600 hover:text-blue-800 underline underline-offset-4"
                  >
                    ← Back to Sign In
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // WebUser registration
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className="absolute inset-0 bg-blue-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <Link href="/">SaaS User Auth</Link>
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              &ldquo;Join thousands of users who trust our platform for their business needs.&rdquo;
            </p>
            <footer className="text-sm">New User Welcome</footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <Card>
            <CardHeader>
              <CardTitle className="text-blue-700">Sign Up</CardTitle>
              <CardDescription>
                Create your account (Standard role by default)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SignUpForm />
            </CardContent>
            <CardContent className="pt-0">
              <div className="text-center text-sm text-muted-foreground">
                Already have an account?{' '}
                <Link
                  href="/auth/wuser/signin"
                  className="underline underline-offset-4 hover:text-primary text-blue-600"
                >
                  Sign In
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
