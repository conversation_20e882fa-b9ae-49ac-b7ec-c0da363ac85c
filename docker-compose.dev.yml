version: '3.8'

services:
  # Development Frontend (with hot reload)
  frontend-dev:
    build:
      context: ./2-frontend-service
      dockerfile: Dockerfile.dev
    container_name: saas-frontend-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    volumes:
      - ./2-frontend-service:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend-dev
    networks:
      - dev-network
    restart: unless-stopped

  # Development Backend (with hot reload)
  backend-dev:
    build:
      context: ./3-backend-service
      dockerfile: Dockerfile.dev
    container_name: saas-backend-dev
    ports:
      - "3001:3001"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@host.docker.internal:5432/${POSTGRES_DB}
      - REDIS_URL=redis://host.docker.internal:6379
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - FACEBOOK_CLIENT_ID=${FACEBOOK_CLIENT_ID}
      - FACEBOOK_CLIENT_SECRET=${FACEBOOK_CLIENT_SECRET}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    volumes:
      - ./3-backend-service:/app
      - /app/node_modules
      - /app/dist
    # No dependencies needed since we use external database services
    networks:
      - dev-network
    restart: unless-stopped

  # Note: Using external production database services (PostgreSQL and Redis)
  # running on host.docker.internal:5432 and host.docker.internal:6379

  # Prisma Studio (for database management)
  prisma-studio:
    build:
      context: ./3-backend-service
      dockerfile: Dockerfile.dev
    container_name: saas-prisma-studio
    ports:
      - "5555:5555"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@host.docker.internal:5432/${POSTGRES_DB}
    command: ["yarn", "db:studio", "--hostname", "0.0.0.0"]
    volumes:
      - ./3-backend-service:/app
    networks:
      - dev-network
    restart: unless-stopped
    profiles:
      - tools

networks:
  dev-network:
    driver: bridge

# No volumes needed since we use external database services
