saas-userauth1/
├── README.md                           # Main project documentation
├── package.json                        # Root package.json (monorepo)
├── .env.example                        # Environment variables template
├── .gitignore                          # Git ignore rules
├── 1.TechStack.txt                     # Original tech stack requirements
│
├── apps/                               # Applications
│   ├── frontend/                       # Next.js 15 Frontend Application
│   │   ├── src/
│   │   │   ├── app/                    # Next.js App Router
│   │   │   │   ├── auth/               # Authentication pages
│   │   │   │   │   ├── signin/         # Sign in page
│   │   │   │   │   ├── signup/         # Sign up page
│   │   │   │   │   ├── forgot-password/ # Forgot password page
│   │   │   │   │   └── reset-password/ # Reset password page
│   │   │   │   ├── dashboard/          # Dashboard pages
│   │   │   │   │   ├── admin/          # Admin dashboard
│   │   │   │   │   └── user/           # User dashboard
│   │   │   │   ├── globals.css         # Global styles
│   │   │   │   ├── layout.tsx          # Root layout
│   │   │   │   └── page.tsx            # Home page
│   │   │   ├── components/             # React components
│   │   │   │   ├── ui/                 # Shadcn/UI components
│   │   │   │   │   ├── button.tsx
│   │   │   │   │   ├── card.tsx
│   │   │   │   │   └── toaster.tsx
│   │   │   │   ├── auth/               # Auth-specific components
│   │   │   │   ├── dashboard/          # Dashboard components
│   │   │   │   └── forms/              # Form components
│   │   │   ├── lib/                    # Utilities and configurations
│   │   │   │   ├── apollo-wrapper.tsx  # Apollo Client wrapper
│   │   │   │   ├── auth-provider.tsx   # Authentication context
│   │   │   │   └── utils.ts            # Utility functions
│   │   │   ├── hooks/                  # Custom React hooks
│   │   │   ├── types/                  # TypeScript type definitions
│   │   │   └── graphql/                # GraphQL queries and mutations
│   │   ├── public/                     # Static assets
│   │   ├── next.config.js              # Next.js configuration
│   │   ├── tailwind.config.js          # Tailwind CSS configuration
│   │   ├── postcss.config.js           # PostCSS configuration
│   │   ├── tsconfig.json               # TypeScript configuration
│   │   └── package.json                # Frontend dependencies
│   │
│   └── backend/                        # NestJS Backend Application
│       ├── src/
│       │   ├── auth/                   # Authentication module
│       │   │   ├── guards/             # Authentication guards
│       │   │   ├── strategies/         # Passport strategies
│       │   │   ├── dto/                # Data transfer objects
│       │   │   └── resolvers/          # GraphQL resolvers
│       │   ├── users/                  # Users module
│       │   │   ├── dto/                # User DTOs
│       │   │   ├── entities/           # User entities
│       │   │   └── resolvers/          # User resolvers
│       │   ├── subscription/           # Subscription module
│       │   ├── payment/                # Payment module (Stripe)
│       │   ├── database/               # Database module
│       │   │   ├── database.module.ts
│       │   │   └── prisma.service.ts   # Prisma service
│       │   ├── common/                 # Shared utilities
│       │   ├── config/                 # Configuration modules
│       │   ├── app.module.ts           # Main application module
│       │   └── main.ts                 # Application entry point
│       ├── prisma/                     # Prisma configuration
│       │   ├── schema.prisma           # Database schema
│       │   └── seed.ts                 # Database seeding script
│       ├── nest-cli.json               # NestJS CLI configuration
│       ├── tsconfig.json               # TypeScript configuration
│       └── package.json                # Backend dependencies
│
├── packages/                           # Shared packages
│   ├── shared/                         # Shared types and utilities
│   │   ├── types/
│   │   │   └── user.ts                 # User type definitions
│   │   ├── index.ts                    # Package exports
│   │   └── package.json                # Shared package dependencies
│   │
│   └── ui/                             # Shared UI components
│       ├── components/                 # Reusable UI components
│       ├── index.ts                    # Package exports
│       └── package.json                # UI package dependencies
│
├── docs/                               # Documentation
│   ├── SETUP.md                        # Setup and installation guide
│   ├── PROJECT_STRUCTURE.md            # Detailed project structure
│   ├── API.md                          # API documentation (to be created)
│   └── DEPLOYMENT.md                   # Deployment guide (to be created)
│
└── tools/                              # Build tools and scripts
    └── (build scripts and tools)

Key Features:
- Monorepo structure with workspaces
- Next.js 15 with App Router
- NestJS with GraphQL API
- PostgreSQL with Prisma ORM
- Role-based authentication (AdminUsers, WebUsers)
- OAuth integration (Google, Facebook)
- Subscription management with Stripe
- Redis caching
- TypeScript throughout
- Shared packages for code reuse
