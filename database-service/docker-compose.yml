version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: saas-postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-saas_userauth}
      - POSTGRES_USER=${POSTGRES_USER:-saas_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-your_secure_password}
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d
      - ./backups:/backups
    networks:
      - database-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-saas_user} -d ${POSTGRES_DB:-saas_userauth}"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: saas-redis
    command: >
      redis-server
      --appendonly yes
      --requirepass ${REDIS_PASSWORD:-your_redis_password}
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-your_redis_password}
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - database-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Database Backup Service
  db-backup:
    image: postgres:15-alpine
    container_name: saas-db-backup
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-saas_userauth}
      - POSTGRES_USER=${POSTGRES_USER:-saas_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-your_secure_password}
      - PGPASSWORD=${POSTGRES_PASSWORD:-your_secure_password}
    volumes:
      - ./backups:/backups
      - ./backup-scripts:/scripts
    networks:
      - database-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: "no"
    profiles:
      - backup
    command: >
      sh -c "
        while true; do
          echo 'Creating backup...'
          pg_dump -h postgres -U ${POSTGRES_USER:-saas_user} -d ${POSTGRES_DB:-saas_userauth} > /backups/backup_$(date +%Y%m%d_%H%M%S).sql
          echo 'Backup created successfully'
          # Keep only last 7 days of backups
          find /backups -name '*.sql' -mtime +7 -delete
          sleep 86400  # 24 hours
        done
      "

networks:
  database-network:
    driver: bridge
    external: false

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
