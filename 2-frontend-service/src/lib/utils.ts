import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(input: string | number | Date): string {
  const date = new Date(input)
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  })
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

export function getInitials(name: string): string {
  return name
    .split(' ')
    .map((word) => word[0])
    .join('')
    .toUpperCase()
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validatePassword(password: string): {
  isValid: boolean
  message: string
} {
  const minLength = 8
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

  if (password.length < minLength) {
    return {
      isValid: false,
      message: `Password must be at least ${minLength} characters long`,
    }
  }

  if (!hasUpperCase || !hasLowerCase) {
    return {
      isValid: false,
      message: 'Password must contain both uppercase and lowercase letters',
    }
  }

  if (!hasNumbers) {
    return {
      isValid: false,
      message: 'Password must contain at least one number',
    }
  }

  if (!hasSpecialChar) {
    return {
      isValid: false,
      message: 'Password must contain at least one special character',
    }
  }

  return {
    isValid: true,
    message: 'Password is valid',
  }
}

export function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }

    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export function generatePasswordResetToken(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15)
}

export function isValidSubscriptionTier(tier: string): boolean {
  const validTiers = ['FREE', 'STARTER', 'PRO']
  return validTiers.includes(tier.toUpperCase())
}

export function getSubscriptionFeatures(tier: string): string[] {
  const features = {
    FREE: [
      'Basic features',
      '100 API calls/month',
      'Community support',
    ],
    STARTER: [
      'All Free features',
      '1,000 API calls/month',
      'Priority support',
      'Advanced analytics',
    ],
    PRO: [
      'All Starter features',
      'Unlimited API calls',
      '24/7 Premium support',
      'Custom integrations',
      'Dedicated account manager',
    ],
  }

  return features[tier as keyof typeof features] || features.FREE
}
