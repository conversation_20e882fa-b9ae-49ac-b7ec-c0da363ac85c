# Development Frontend Dockerfile - Next.js 15
FROM node:20-alpine

# Update packages and install dependencies
RUN apk update && apk upgrade && apk add --no-cache libc6-compat curl

WORKDIR /app

# Install Yarn
RUN corepack enable && corepack prepare yarn@stable --activate

# Copy package files
COPY package.json yarn.lock* .yarnrc.yml ./

# Install dependencies
RUN yarn install

# Copy source code
COPY . .

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start development server
CMD ["yarn", "dev"]
