import { Metadata } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { SignInForm } from '@/components/auth/signin-form'

export const metadata: Metadata = {
  title: 'AdminUser Sign In - SaaS User Auth',
  description: 'Sign in to your AdminUser account',
}

export default function AdminUserSignInPage() {
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className="absolute inset-0 bg-red-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <Link href="/">SaaS User Auth</Link>
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              &ldquo;The AdminUser interface gives me complete control over the system and user management.&rdquo;
            </p>
            <footer className="text-sm">AdminUser - System Administrator</footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <Card>
            <CardHeader>
              <CardTitle className="text-red-700">AdminUser Sign In</CardTitle>
              <CardDescription>
                Sign in to your AdminUser account (Administrative Access Only)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SignInForm expectedRole="ADMINUSER" />
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-xs text-red-700">
                  <strong>AdminUser Access:</strong> Only authorized administrators can access this login. 
                  AdminUser accounts cannot be self-registered and must be created by existing AdminUsers.
                </p>
              </div>
              <div className="text-center text-sm text-muted-foreground">
                Need WebUser access?{' '}
                <Link
                  href="/auth/webuser/signin"
                  className="underline underline-offset-4 hover:text-primary text-blue-600"
                >
                  WebUser Login
                </Link>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
