-- Migration script to update roles from USER/ADMIN to WEBUSER/ADMINUSER

-- First, add the new enum values
ALTER TYPE "Role" ADD VALUE 'WEBUSER';
ALTER TYPE "Role" ADD VALUE 'ADMINUSER';

-- Update existing data
UPDATE "users" SET "role" = 'WEBUSER' WHERE "role" = 'USER';
UPDATE "users" SET "role" = 'ADMINUSER' WHERE "role" = 'ADMIN';

-- Note: We'll remove the old enum values in a separate migration after confirming the update worked
