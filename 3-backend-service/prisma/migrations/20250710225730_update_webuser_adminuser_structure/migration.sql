/*
  Warnings:

  - The values [STANDA<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,AGENC<PERSON>] on the enum `Role` will be removed. If these variants are still used in the database, this will fail.

*/
-- CreateEnum
CREATE TYPE "WebUserRole" AS ENUM ('STANDARD', 'DEVCOMPANY', 'AGENCY');

-- AlterEnum
BEGIN;
CREATE TYPE "Role_new" AS ENUM ('WEBUSER', 'ADMINUSER');
ALTER TABLE "users" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "users" ALTER COLUMN "role" TYPE "Role_new" USING ("role"::text::"Role_new");
ALTER TYPE "Role" RENAME TO "Role_old";
ALTER TYPE "Role_new" RENAME TO "Role";
DROP TYPE "Role_old";
ALTER TABLE "users" ALTER COLUMN "role" SET DEFAULT 'WEBUSER';
COMMIT;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "webUserRole" TEXT,
ALTER COLUMN "role" SET DEFAULT 'WEBUSER';
