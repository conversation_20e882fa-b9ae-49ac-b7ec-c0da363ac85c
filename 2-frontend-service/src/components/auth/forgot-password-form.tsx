'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { gql, useMutation } from '@apollo/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { type GraphQLError, type ForgotPasswordFormData, type ForgotPasswordMutationResponse } from '@/types'

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
})

const FORGOT_PASSWORD_MUTATION = gql`
  mutation ForgotPassword($email: String!) {
    forgotPassword(email: $email)
  }
`

export function ForgotPasswordForm() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const [forgotPassword] = useMutation<ForgotPasswordMutationResponse>(FORGOT_PASSWORD_MUTATION, {
    onCompleted: () => {
      setIsLoading(false)
      toast({
        title: 'Success',
        description: 'If an account exists with this email, you will receive a password reset link.',
      })
      router.push('/auth/signin')
    },
    onError: (error: GraphQLError) => {
      setIsLoading(false)
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  const form = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  })

  async function onSubmit(data: ForgotPasswordFormData) {
    setIsLoading(true)
    try {
      await forgotPassword({
        variables: {
          email: data.email,
        },
      })
    } catch (error) {
      // Error is handled by onError callback
    }
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          {...form.register('email')}
        />
        {form.formState.errors.email && (
          <p className="text-sm text-red-500">
            {form.formState.errors.email.message}
          </p>
        )}
      </div>
      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading ? 'Sending reset link...' : 'Send reset link'}
      </Button>
    </form>
  )
}
