# Microservices Structure

## New Standalone Services Architecture

```
saas-userauth1/
├── frontend-service/           # Standalone Frontend Microservice
│   ├── src/                   # Next.js 15 source code
│   ├── public/                # Static assets
│   ├── package.json           # Independent dependencies
│   ├── Dockerfile             # Production build
│   ├── Dockerfile.dev         # Development build
│   ├── next.config.js         # Next.js configuration
│   ├── tailwind.config.js     # Tailwind CSS config
│   └── tsconfig.json          # TypeScript config
│
├── backend-service/           # Standalone Backend Microservice
│   ├── src/                   # NestJS source code
│   ├── prisma/                # Database schema & migrations
│   ├── package.json           # Independent dependencies
│   ├── Dockerfile             # Production build
│   ├── Dockerfile.dev         # Development build
│   ├── nest-cli.json          # NestJS CLI config
│   └── tsconfig.json          # TypeScript config
│
├── nginx-service/             # Standalone NGINX Proxy
│   ├── conf.d/                # NGINX configurations
│   ├── ssl/                   # SSL certificates
│   ├── logs/                  # Log files
│   ├── Dockerfile             # NGINX container
│   └── nginx.conf             # Main NGINX config
│
├── database-service/          # Standalone Database Service
│   ├── init/                  # Database initialization scripts
│   ├── backups/               # Database backups
│   ├── backup-scripts/        # Backup automation
│   ├── docker-compose.yml     # Database stack
│   └── redis.conf             # Redis configuration
│
├── docker-compose.yml         # Main orchestration
├── docker-compose.dev.yml     # Development environment
├── docker-compose.prod.yml    # Production environment
├── .env.example               # Environment template
└── scripts/                   # Deployment & management scripts
```

## Service Independence

### 1. Frontend Service
- **Completely standalone** Next.js 15 application
- **Own package.json** with all required dependencies
- **Independent Docker build** process
- **No shared dependencies** with other services
- **Direct API communication** via HTTP/GraphQL

### 2. Backend Service  
- **Completely standalone** NestJS application
- **Own package.json** with all required dependencies
- **Independent Docker build** process
- **Own Prisma schema** and database management
- **No shared dependencies** with other services

### 3. NGINX Service
- **Standalone proxy server** with custom configuration
- **Load balancing** between multiple backend instances
- **SSL termination** and security headers
- **Static file serving** and caching
- **Rate limiting** and DDoS protection

### 4. Database Service
- **Standalone PostgreSQL** with optimized configuration
- **Standalone Redis** for caching and sessions
- **Automated backups** and monitoring
- **Independent scaling** and maintenance

## Network Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    NGINX Proxy Service                     │
│                   (nginx-service)                          │
│                     Port: 80/443                           │
└─────────────────────┬───────────────────────────────────────┘
                      │ proxy-network
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  Frontend   │ │   Backend   │ │   Backend   │
│  Service    │ │   Service   │ │  Service-2  │
│ Port: 3000  │ │ Port: 3001  │ │ Port: 3002  │
└─────────────┘ └─────────────┘ └─────────────┘
                      │             │
                      └─────┬───────┘
                            │ database-network
        ┌───────────────────┼───────────────────┐
        │                   │                   │
        ▼                   ▼                   ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ PostgreSQL  │ │    Redis    │ │   Backup    │
│ Port: 5432  │ │ Port: 6379  │ │   Service   │
└─────────────┘ └─────────────┘ └─────────────┘
```

## Deployment Commands

### Development
```bash
# Start all services in development mode
docker-compose -f docker-compose.dev.yml up

# Start specific service
docker-compose -f docker-compose.dev.yml up frontend-dev
docker-compose -f docker-compose.dev.yml up backend-dev

# Start database services only
cd database-service && docker-compose up
```

### Production
```bash
# Start all services in production mode
docker-compose up -d

# Scale backend services
docker-compose up -d --scale backend=3

# Start with scaling profile
docker-compose --profile scaling up -d
```

### Individual Services
```bash
# Frontend service only
cd frontend-service && docker build -t saas-frontend .
docker run -p 3000:3000 saas-frontend

# Backend service only  
cd backend-service && docker build -t saas-backend .
docker run -p 3001:3001 saas-backend

# NGINX service only
cd nginx-service && docker build -t saas-nginx .
docker run -p 80:80 saas-nginx

# Database services only
cd database-service && docker-compose up
```

## Benefits of This Architecture

### 1. **True Microservices**
- Each service is completely independent
- No shared dependencies or monorepo complexity
- Individual deployment and scaling
- Technology stack flexibility

### 2. **Development Flexibility**
- Work on services independently
- Different teams can own different services
- Faster development cycles
- Easier testing and debugging

### 3. **Production Benefits**
- Independent scaling of each service
- Better fault isolation
- Easier maintenance and updates
- Optimized resource usage

### 4. **DevOps Advantages**
- Individual CI/CD pipelines
- Service-specific monitoring
- Independent backup strategies
- Easier rollbacks and deployments

## Migration from Monorepo

The old `apps/` structure has been converted to standalone services:
- `apps/frontend/` → `frontend-service/`
- `apps/backend/` → `backend-service/`
- `nginx/` → `nginx-service/`
- New `database-service/` for data layer

Each service now has its own:
- Dependencies and package.json
- Docker build process
- Configuration files
- Development environment
