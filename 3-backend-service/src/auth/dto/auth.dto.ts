import { InputType, Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { IsEmail, IsNotEmpty, MinLength, IsOptional, IsEnum, IsIn } from 'class-validator';
import { Role } from '@prisma/client';

// Register the Role enum for GraphQL
registerEnumType(Role, {
  name: 'Role',
  description: 'User role types',
});

@InputType()
export class SignUpInput {
  @Field()
  @IsEmail()
  email: string;

  @Field()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @Field({ nullable: true })
  @IsOptional()
  name?: string;

  @Field(() => Role, { nullable: true })
  @IsOptional()
  @IsIn([Role.STANDARDUSER, Role.DEVCOMPANY, Role.AGENCY]) // Only allow WebUser roles for public signup
  role?: Role;
}

@InputType()
export class SignInInput {
  @Field()
  @IsEmail()
  email: string;

  @Field()
  @IsNotEmpty()
  password: string;
}

@InputType()
export class ForgotPasswordInput {
  @Field()
  @IsEmail()
  email: string;
}

@InputType()
export class ResetPasswordInput {
  @Field()
  @IsNotEmpty()
  token: string;

  @Field()
  @IsNotEmpty()
  @MinLength(6)
  password: string;
}
