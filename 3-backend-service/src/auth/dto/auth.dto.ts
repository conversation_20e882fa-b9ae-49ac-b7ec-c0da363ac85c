import { InputType, Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { IsEmail, IsNotEmpty, MinLength, IsOptional, IsEnum, IsIn } from 'class-validator';
import { Role, WebUserRole } from '@prisma/client';

// Register the Role enum for GraphQL
registerEnumType(Role, {
  name: 'Role',
  description: 'User role types',
});

// Register the WebUserRole enum for GraphQL
registerEnumType(WebUserRole, {
  name: 'WebUserRole',
  description: 'Web user role subtypes',
});

@InputType()
export class SignUpInput {
  @Field()
  @IsEmail()
  email: string;

  @Field()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @Field({ nullable: true })
  @IsOptional()
  name?: string;

  @Field(() => WebUserRole, { nullable: true })
  @IsOptional()
  @IsEnum(WebUserRole) // Only allow WebUser role subtypes for public signup
  webUserRole?: WebUserRole;
}

@InputType()
export class SignInInput {
  @Field()
  @IsEmail()
  email: string;

  @Field()
  @IsNotEmpty()
  password: string;
}

@InputType()
export class ForgotPasswordInput {
  @Field()
  @IsEmail()
  email: string;
}

@InputType()
export class ResetPasswordInput {
  @Field()
  @IsNotEmpty()
  token: string;

  @Field()
  @IsNotEmpty()
  @MinLength(6)
  password: string;
}
