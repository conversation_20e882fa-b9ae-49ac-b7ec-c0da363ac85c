import { Metadata } from 'next'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { SignInForm } from '@/components/auth/signin-form'

interface SharedSignInPageProps {
  userType: 'wuser' | 'auser'
  metadata: Metadata
}

export function SharedSignInPage({ userType, metadata }: SharedSignInPageProps) {
  const isAdmin = userType === 'auser'
  
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className={`absolute inset-0 ${isAdmin ? 'bg-red-900' : 'bg-blue-900'}`} />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <Link href="/">SaaS User Auth</Link>
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              {isAdmin 
                ? "&ldquo;Administrative access provides complete system control and management capabilities.&rdquo;"
                : "&ldquo;This authentication system has streamlined our business processes and made our application more secure.&rdquo;"
              }
            </p>
            <footer className="text-sm">
              {isAdmin ? 'System Administrator' : 'Satisfied Customer'}
            </footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <Card>
            <CardHeader>
              <CardTitle className={isAdmin ? 'text-red-700' : 'text-blue-700'}>
                {isAdmin ? 'Admin Access' : 'Sign In'}
              </CardTitle>
              <CardDescription>
                {isAdmin 
                  ? 'Administrative login - Authorized personnel only'
                  : 'Enter your credentials to access your account'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SignInForm />
            </CardContent>
            <CardContent className="pt-0">
              {!isAdmin && (
                <div className="text-center text-sm text-muted-foreground">
                  Don&apos;t have an account?{' '}
                  <Link
                    href="/wuser/signup"
                    className="underline underline-offset-4 hover:text-primary text-blue-600"
                  >
                    Sign up
                  </Link>
                </div>
              )}
              {isAdmin && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-xs text-red-700">
                    <strong>Admin Access:</strong> Only authorized administrators can access this login. 
                    Admin accounts cannot be self-registered.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
