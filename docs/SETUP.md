# 🚀 Microservices Setup Guide

## Prerequisites

- Docker & Docker Compose
- Git
- Text editor (VS Code recommended)

## 🔧 Quick Setup

### 1. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings:
# - Database credentials
# - JWT secret
# - OAuth keys (Google, Facebook)
# - Stripe keys
# - Email configuration
```

### 2. Development Environment
```bash
# Start all services in development mode
./scripts/start-dev.sh

# Services will be available at:
# Frontend:  http://localhost:3000
# Backend:   http://localhost:3001
# GraphQL:   http://localhost:3001/graphql
# Database:  localhost:5432
# Redis:     localhost:6379
```

### 3. Production Environment
```bash
# Start all services in production mode
./scripts/start-prod.sh

# Application will be available at:
# http://localhost (via NGINX)
```

## 🛠️ Individual Service Development

### Frontend Service
```bash
cd frontend-service
docker build -t saas-frontend .
docker run -p 3000:3000 saas-frontend
```

### Backend Service
```bash
cd backend-service
docker build -t saas-backend .
docker run -p 3001:3001 saas-backend
```

### Database Services
```bash
cd database-service
docker-compose up -d
```

## 🔍 Service URLs

### Development
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- GraphQL Playground: http://localhost:3001/graphql
- PostgreSQL: localhost:5432
- Redis: localhost:6379

### Production
- Application: http://localhost (via NGINX)
- All services behind NGINX proxy

## 🐳 Docker Commands

### Development
```bash
# Start development environment
./scripts/start-dev.sh

# Stop development environment
./scripts/stop-dev.sh

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Rebuild services
docker-compose -f docker-compose.dev.yml build
```

### Production
```bash
# Start production environment
./scripts/start-prod.sh

# Stop production environment
./scripts/stop-prod.sh

# Scale backend services
docker-compose up -d --scale backend=3

# View logs
docker-compose logs -f
```

## OAuth Setup

### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google`
   - `http://localhost:3001/auth/google/callback`

### Facebook OAuth
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Configure OAuth redirect URIs:
   - `http://localhost:3000/api/auth/callback/facebook`
   - `http://localhost:3001/auth/facebook/callback`

## Stripe Setup

1. Create account at [Stripe](https://stripe.com)
2. Get test API keys from dashboard
3. Set up webhooks for subscription events
4. Add webhook endpoint: `http://localhost:3001/webhooks/stripe`
