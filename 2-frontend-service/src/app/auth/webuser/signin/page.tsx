import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { SignInForm } from '@/components/auth/signin-form'

export const metadata: Metadata = {
  title: 'WebUser Sign In - SaaS User Auth',
  description: 'Sign in to your WebUser account',
}

export default function WebUserSignInPage() {
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className="absolute inset-0 bg-blue-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <Link href="/">SaaS User Auth</Link>
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              &ldquo;As a WebUser, I love the intuitive interface and powerful features available to me.&rdquo;
            </p>
            <footer className="text-sm">WebUser - Standard Account</footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <Card>
            <CardHeader>
              <CardTitle className="text-blue-700">WebUser Sign In</CardTitle>
              <CardDescription>
                Sign in to your WebUser account (Standard, DevCompany, or Agency)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SignInForm expectedRole="WEBUSER" />
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <div className="text-center text-sm text-muted-foreground">
                Don&apos;t have a WebUser account?{' '}
                <Link
                  href="/auth/signup"
                  className="underline underline-offset-4 hover:text-primary text-blue-600"
                >
                  Sign up as WebUser
                </Link>
              </div>
              <div className="text-center text-sm text-muted-foreground">
                Are you an AdminUser?{' '}
                <Link
                  href="/auth/adminuser/signin"
                  className="underline underline-offset-4 hover:text-primary text-red-600"
                >
                  AdminUser Login
                </Link>
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
