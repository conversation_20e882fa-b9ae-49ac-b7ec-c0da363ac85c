{"name": "@saas-userauth/ui", "version": "1.0.0", "description": "Shared UI components", "main": "index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist node_modules"}, "dependencies": {"react": "^18.2.0", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"typescript": "^5.3.3", "@types/react": "^18.2.42"}}