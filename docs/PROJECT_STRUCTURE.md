# Project Structure

```
saas-userauth1/
├── apps/
│   ├── frontend/                 # Next.js 15 Frontend
│   │   ├── src/
│   │   │   ├── app/             # App Router pages
│   │   │   │   ├── auth/        # Authentication pages
│   │   │   │   │   ├── signin/
│   │   │   │   │   ├── signup/
│   │   │   │   │   ├── forgot-password/
│   │   │   │   │   └── reset-password/
│   │   │   │   ├── dashboard/   # Dashboard pages
│   │   │   │   │   ├── admin/   # Admin dashboard
│   │   │   │   │   └── user/    # User dashboard
│   │   │   │   ├── globals.css
│   │   │   │   ├── layout.tsx
│   │   │   │   └── page.tsx
│   │   │   ├── components/      # React components
│   │   │   │   ├── ui/          # Shadcn/UI components
│   │   │   │   ├── auth/        # Auth-specific components
│   │   │   │   ├── dashboard/   # Dashboard components
│   │   │   │   └── forms/       # Form components
│   │   │   ├── lib/             # Utilities and configs
│   │   │   │   ├── apollo-wrapper.tsx
│   │   │   │   ├── auth-provider.tsx
│   │   │   │   └── utils.ts
│   │   │   ├── hooks/           # Custom React hooks
│   │   │   ├── types/           # TypeScript types
│   │   │   └── graphql/         # GraphQL queries/mutations
│   │   ├── public/              # Static assets
│   │   ├── next.config.js
│   │   ├── tailwind.config.js
│   │   ├── tsconfig.json
│   │   └── package.json
│   │
│   └── backend/                 # NestJS Backend
│       ├── src/
│       │   ├── auth/            # Authentication module
│       │   │   ├── guards/      # Auth guards
│       │   │   ├── strategies/  # Passport strategies
│       │   │   ├── dto/         # Data transfer objects
│       │   │   └── resolvers/   # GraphQL resolvers
│       │   ├── users/           # Users module
│       │   │   ├── dto/
│       │   │   ├── entities/
│       │   │   └── resolvers/
│       │   ├── subscription/    # Subscription module
│       │   ├── payment/         # Payment module (Stripe)
│       │   ├── database/        # Database module
│       │   │   └── prisma.service.ts
│       │   ├── common/          # Shared utilities
│       │   ├── config/          # Configuration
│       │   ├── app.module.ts
│       │   └── main.ts
│       ├── prisma/
│       │   ├── schema.prisma    # Database schema
│       │   └── seed.ts          # Database seeding
│       ├── nest-cli.json
│       ├── tsconfig.json
│       └── package.json
│
├── packages/
│   ├── shared/                  # Shared types and utilities
│   │   ├── types/
│   │   │   └── user.ts
│   │   ├── index.ts
│   │   └── package.json
│   │
│   └── ui/                      # Shared UI components
│       ├── components/
│       ├── index.ts
│       └── package.json
│
├── docs/                        # Documentation
│   ├── SETUP.md
│   ├── PROJECT_STRUCTURE.md
│   ├── YARN_GUIDE.md
│   ├── API.md
│   └── DEPLOYMENT.md
│
├── tools/                       # Build tools and scripts
│
├── .env.example                 # Environment variables template
├── .gitignore
├── package.json                 # Root package.json (monorepo)
└── README.md
```

## Key Features by Module

### Frontend (Next.js 15)
- **App Router**: Modern Next.js routing
- **Authentication Pages**: Sign in, Sign up, Password reset
- **Dashboards**: Separate for Admin and Web users
- **Role-based UI**: Different interfaces based on user roles
- **GraphQL Integration**: Apollo Client for API communication
- **Responsive Design**: Tailwind CSS + Shadcn/UI

### Backend (NestJS)
- **GraphQL API**: Apollo Server integration
- **Authentication**: Local + OAuth (Google, Facebook)
- **Authorization**: Role-based access control
- **Database**: PostgreSQL with Prisma ORM
- **Payments**: Stripe integration
- **Caching**: Redis for session management
- **Email**: Password reset functionality

### Database Schema
- **Users**: Admin and Web users with different roles
- **Subscriptions**: Free, Starter, Pro plans
- **Payments**: Stripe payment tracking
- **Sessions**: JWT token management

### Shared Packages
- **Types**: Common TypeScript interfaces
- **UI Components**: Reusable React components
- **Utilities**: Shared helper functions

### Package Management
- **Yarn Workspaces**: Monorepo management
- **Commands**: `yarn dev`, `yarn build`, `yarn workspace <name> <command>`
- **Dependencies**: Managed per workspace with shared root dependencies
