"use client"

import { useState, type FormEvent } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { gql, useMutation } from '@apollo/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { useAuth } from '@/lib/auth-provider'

const signInSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
})

type SignInValues = z.infer<typeof signInSchema>

const SIGN_IN_MUTATION = gql`
  mutation SignIn($input: SignInInput!) {
    signIn(input: $input) {
      token
      user {
        id
        email
        name
        role
      }
    }
  }
`

interface SignInFormProps {
  expectedRole?: 'WEBUSER' | 'ADMINUSER'
}

export function SignInForm({ expectedRole }: SignInFormProps = {}) {
  const router = useRouter()
  const { toast } = useToast()
  const { signIn: authSignIn } = useAuth()
  const [isLoading, setIsLoading] = useState(false)

  const [signIn] = useMutation(SIGN_IN_MUTATION, {
    onCompleted: (data) => {
      setIsLoading(false)
      toast({
        title: 'Success',
        description: 'Successfully signed in',
      })
      // Use auth provider to handle token and redirect
      authSignIn(data.signIn.token, data.signIn.user)
    },
    onError: (error) => {
      setIsLoading(false)
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  const form = useForm<SignInValues>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  async function onSubmit(data: SignInValues) {
    setIsLoading(true)
    try {
      await signIn({
        variables: {
          input: {
            email: data.email,
            password: data.password,
          },
        },
      })
    } catch (error) {
      // Error is handled by onError callback
    }
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          {...form.register('email')}
        />
        {form.formState.errors.email && (
          <p className="text-sm text-red-500">
            {form.formState.errors.email.message}
          </p>
        )}
      </div>
      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <Input
          id="password"
          type="password"
          {...form.register('password')}
        />
        {form.formState.errors.password && (
          <p className="text-sm text-red-500">
            {form.formState.errors.password.message}
          </p>
        )}
      </div>
      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading ? 'Signing in...' : 'Sign in'}
      </Button>
    </form>
  )
}
