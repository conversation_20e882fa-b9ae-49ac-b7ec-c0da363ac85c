#!/bin/bash

# Enhanced Development Environment with Auth Testing
echo "🚀 Starting SaaS User Auth - Development Environment with Auth Testing"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${RED}⚠️  .env file not found. Copying from .env.example...${NC}"
    cp .env.example .env
    echo -e "${YELLOW}📝 Please edit .env file with your configuration${NC}"
    exit 1
fi

# Function to check if service is ready
check_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${BLUE}⏳ Waiting for $service_name to be ready...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready!${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}   Attempt $attempt/$max_attempts - $service_name not ready yet...${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ $service_name failed to start after $max_attempts attempts${NC}"
    return 1
}

# Start database services first
echo -e "${BLUE}🗄️  Starting database services...${NC}"
cd 4-database-service
docker-compose up -d
cd ..

# Wait for databases to be ready
echo -e "${BLUE}⏳ Waiting for databases to be ready...${NC}"
sleep 10

# Check database connectivity (skip HTTP check for PostgreSQL)
echo -e "${GREEN}✅ Database services started!${NC}"

# Start development services
echo -e "${BLUE}🔧 Starting development services...${NC}"
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to start
echo -e "${BLUE}⏳ Waiting for services to start...${NC}"
sleep 15

# Check service health
echo -e "${BLUE}🔍 Checking service health...${NC}"

# Check Frontend
if check_service "http://localhost:3000" "Frontend (Next.js)"; then
    FRONTEND_STATUS="✅ Ready"
else
    FRONTEND_STATUS="❌ Failed"
fi

# Check Backend
if check_service "http://localhost:3010/health" "Backend (NestJS)"; then
    BACKEND_STATUS="✅ Ready"
else
    BACKEND_STATUS="❌ Failed"
fi

# Check GraphQL
if check_service "http://localhost:3010/graphql" "GraphQL API"; then
    GRAPHQL_STATUS="✅ Ready"
else
    GRAPHQL_STATUS="❌ Failed"
fi

# Display status
echo -e "\n${GREEN}✅ Development environment started!${NC}"
echo -e "\n${BLUE}📊 Service Status:${NC}"
echo -e "   Frontend:  http://localhost:3000 - $FRONTEND_STATUS"
echo -e "   Backend:   http://localhost:3010 - $BACKEND_STATUS"
echo -e "   GraphQL:   http://localhost:3010/graphql - $GRAPHQL_STATUS"
echo -e "   Database:  localhost:5432 - ✅ Ready"
echo -e "   Redis:     localhost:6379 - ✅ Ready"

echo -e "\n${BLUE}🔐 Auth Testing Endpoints:${NC}"
echo -e "   Register:  POST http://localhost:3010/auth/register"
echo -e "   Login:     POST http://localhost:3010/auth/login"
echo -e "   Profile:   GET  http://localhost:3010/auth/profile"
echo -e "   GraphQL:   http://localhost:3010/graphql"

echo -e "\n${BLUE}🔍 Development Commands:${NC}"
echo -e "   View logs:     docker-compose -f docker-compose.dev.yml logs -f"
echo -e "   Restart:       docker-compose -f docker-compose.dev.yml restart"
echo -e "   Rebuild:       docker-compose -f docker-compose.dev.yml build"
echo -e "   Prisma Studio: docker-compose -f docker-compose.dev.yml --profile tools up prisma-studio"

echo -e "\n${BLUE}🧪 Quick Auth Test:${NC}"
echo -e "   Test registration: curl -X POST http://localhost:3010/auth/register -H 'Content-Type: application/json' -d '{\"email\":\"<EMAIL>\",\"password\":\"password123\",\"name\":\"Test User\"}'"

echo -e "\n${BLUE}🛑 Stop Environment:${NC}"
echo -e "   ./scripts/stop-dev.sh"

echo -e "\n${GREEN}🎉 Happy coding! Your SaaS development environment is ready!${NC}"
