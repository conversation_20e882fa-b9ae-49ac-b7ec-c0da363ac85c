import { Apollo<PERSON><PERSON>, ApolloProvider, InMemory<PERSON><PERSON>, createHttpLink, type NormalizedCacheObject } from '@apollo/client'
import { setContext } from '@apollo/client/link/context'
import { type ReactNode } from 'react'

const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:3001/graphql',
})

const authLink = setContext((_: unknown, { headers }: { headers?: Record<string, string> }) => {
  // Get the authentication token from local storage if it exists
  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null

  // Return the headers to the context so httpL<PERSON> can read them
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
    },
  }
})

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
    },
  },
})

export function ApolloWrapper({ children }: { children: ReactNode }) {
  return <ApolloProvider client={client}>{children}</ApolloProvider>
}
