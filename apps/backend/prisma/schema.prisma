// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserType {
  ADMIN_USER
  WEB_USER
}

enum AdminRole {
  SUPER_USER
  ADMIN
  MODERATOR
}

enum WebUserRole {
  DEV_COMPANY
  AGENCY
  STANDARD_USER
}

enum SubscriptionPlan {
  FREE
  STARTER
  PRO
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  PAST_DUE
}

enum AuthProvider {
  LOCAL
  GOOGLE
  FACEBOOK
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String?  @unique
  mobile    String?  @unique
  password  String?  // Nullable for OAuth users
  
  // Profile information
  firstName String?
  lastName  String?
  avatar    String?
  
  // User type and roles
  userType     UserType
  adminRole    AdminRole?    // Only for ADMIN_USER type
  webUserRole  WebUserRole?  // Only for WEB_USER type
  
  // Authentication
  isEmailVerified Boolean @default(false)
  isMobileVerified Boolean @default(false)
  isActive        Boolean @default(true)
  
  // OAuth data
  authProviders AuthProvider[]
  googleId      String?
  facebookId    String?
  
  // Password reset
  resetToken    String?
  resetTokenExpiry DateTime?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastLoginAt DateTime?
  
  // Relations
  subscription Subscription?
  payments     Payment[]
  sessions     Session[]
  
  // Admin relations
  createdBy   User?   @relation("AdminCreatedBy", fields: [createdById], references: [id])
  createdById String?
  createdUsers User[] @relation("AdminCreatedBy")
  
  @@map("users")
}

model Subscription {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  plan   SubscriptionPlan
  status SubscriptionStatus @default(INACTIVE)
  
  // Stripe data
  stripeCustomerId     String?
  stripeSubscriptionId String?
  stripePriceId        String?
  
  // Billing
  currentPeriodStart DateTime?
  currentPeriodEnd   DateTime?
  cancelAtPeriodEnd  Boolean   @default(false)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("subscriptions")
}

model Payment {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Payment details
  amount        Int    // Amount in cents
  currency      String @default("usd")
  status        String // succeeded, pending, failed
  
  // Stripe data
  stripePaymentIntentId String?
  stripeChargeId        String?
  
  // Metadata
  description String?
  metadata    Json?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("payments")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  token     String   @unique
  expiresAt DateTime
  
  // Session metadata
  ipAddress String?
  userAgent String?
  
  createdAt DateTime @default(now())
  
  @@map("sessions")
}
