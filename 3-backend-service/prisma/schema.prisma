// This is your Prisma schema file

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  role      Role     @default(WEBUSER)
  verified  <PERSON><PERSON><PERSON>  @default(false)
  
  // Password reset
  resetToken       String?
  resetTokenExpiry DateTime?
  
  // Verification
  verificationToken       String?
  verificationTokenExpiry DateTime?
  
  // OAuth
  googleId String? @unique
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

enum Role {
  WEBUSER
  ADMINUSER
}

// For session management
model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("sessions")
}

// For password reset tokens
model PasswordReset {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  used      Boolean  @default(false)

  @@map("password_resets")
}
