'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/lib/auth-provider'

export default function AdminDashboardPage() {
  const { user } = useAuth()
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <div className="text-sm text-muted-foreground">
          Administrator: {user?.name || user?.email}
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card>
        <CardHeader>
          <CardTitle>WebUser & AdminUser Management</CardTitle>
          <CardDescription>Manage WebUser and AdminUser accounts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">1</div>
          <p className="text-xs text-muted-foreground">Total accounts</p>
          <div className="flex gap-2 mt-2">
            <Button variant="outline" size="sm">View WebUsers</Button>
            <Button variant="default" size="sm">Create AdminUser</Button>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Role Management</CardTitle>
          <CardDescription>Manage WebUser and AdminUser roles and permissions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">3</div>
          <p className="text-xs text-muted-foreground">Available roles</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Subscription Management</CardTitle>
          <CardDescription>Manage WebUser subscriptions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">3</div>
          <p className="text-xs text-muted-foreground">Subscription tiers</p>
        </CardContent>
      </Card>
      </div>

      <div className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>AdminUser Management</CardTitle>
            <CardDescription>Create and manage AdminUsers (AdminUsers cannot self-register)</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 border rounded-lg bg-blue-50">
              <h3 className="font-semibold text-blue-900">AdminUser Creation</h3>
              <p className="text-sm text-blue-700 mt-1">
                Only existing AdminUsers can create new AdminUser accounts. WebUsers register themselves with Standard role.
              </p>
              <Button className="mt-3" variant="default">
                Create New AdminUser
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
