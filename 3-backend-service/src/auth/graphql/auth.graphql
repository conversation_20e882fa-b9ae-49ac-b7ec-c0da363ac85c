type User {
  id: ID!
  email: String!
  name: String!
  role: Role!
  createdAt: DateTime!
  updatedAt: DateTime!
}

enum Role {
  USER
  ADMIN
}

type AuthResponse {
  token: String!
  user: User!
}

input SignInInput {
  email: String!
  password: String!
}

input SignUpInput {
  email: String!
  password: String!
  name: String!
}

input ForgotPasswordInput {
  email: String!
}

input ResetPasswordInput {
  token: String!
  password: String!
}

type Query {
  me: User!
}

type Mutation {
  signIn(input: SignInInput!): AuthResponse!
  signUp(input: SignUpInput!): AuthResponse!
  forgotPassword(input: ForgotPasswordInput!): Boolean!
  resetPassword(input: ResetPasswordInput!): Boolean!
}

scalar DateTime
