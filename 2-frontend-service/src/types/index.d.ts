import { ReactNode } from 'react'
import { ButtonHTMLAttributes, InputHTMLAttributes, HTMLAttributes } from 'react'

declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any
    }
  }
}

// Component Props
export interface LayoutProps {
  children: ReactNode
}

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  asChild?: boolean
  className?: string
}

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  className?: string
}

export interface CardProps extends HTMLAttributes<HTMLDivElement> {
  className?: string
}

export interface LabelProps extends HTMLAttributes<HTMLLabelElement> {
  className?: string
}

export interface ToastProps {
  id: string
  title?: ReactNode
  description?: ReactNode
  action?: ReactNode
  variant?: 'default' | 'destructive'
  duration?: number
  className?: string
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export type ToasterToast = ToastProps

export interface ToastState {
  toasts: ToasterToast[]
  toast: (props: Omit<ToastProps, 'id'>) => void
  dismiss: (toastId?: string) => void
}

export interface ToastActionElement {
  altText?: string
  action?: ReactNode
  cancel?: ReactNode
}

// Auth Types
export interface SignInFormData {
  email: string
  password: string
}

export type UserRole = 'WEBUSER' | 'ADMINUSER'
export type WebUserRole = 'STANDARD' | 'DEVCOMPANY' | 'AGENCY'

export interface SignUpFormData {
  email: string
  password: string
  name: string
  role: UserRole
}

export interface AuthResponse {
  token: string
  user: {
    id: string
    email: string
    name: string
    role: 'WEBUSER' | 'ADMINUSER'
    webUserRole?: 'STANDARD' | 'DEVCOMPANY' | 'AGENCY'
  }
}

export interface GraphQLError {
  message: string
  locations?: { line: number; column: number }[]
  path?: string[]
  extensions?: Record<string, any>
}

export interface MutationResponse<T> {
  data?: T
  errors?: GraphQLError[]
}

export interface SignUpMutationResponse {
  signUp: AuthResponse
}

export interface SignInMutationResponse {
  signIn: AuthResponse
}

export interface ForgotPasswordMutationResponse {
  forgotPassword: boolean
}

export interface ResetPasswordMutationResponse {
  resetPassword: boolean
}

export interface PasswordFormData {
  password: string
  confirmPassword: string
}

export interface ResetPasswordFormData extends PasswordFormData {}

export interface ForgotPasswordFormData {
  email: string
}

// Declare modules
declare module 'react' {
  interface JSX {
    IntrinsicElements: {
      [elemName: string]: any
    }
  }
}

declare module '@radix-ui/react-label' {
  export interface LabelProps {
    className?: string
    children?: ReactNode
  }
}

declare module '@radix-ui/react-slot' {
  export interface SlotProps {
    className?: string
    children?: ReactNode
  }
}
