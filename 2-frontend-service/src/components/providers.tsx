'use client'

import React from 'react'
import { ThemeProvider } from 'next-themes'
import { ApolloWrapper } from '@/lib/apollo-wrapper'
import { AuthProvider } from '@/lib/auth-provider'
import { Toaster } from '@/components/ui/toaster'

interface ProvidersProps {
  children: React.ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <ApolloWrapper>
      <AuthProvider>
        {children}
        <Toaster />
      </AuthProvider>
    </ApolloWrapper>
  )
}
