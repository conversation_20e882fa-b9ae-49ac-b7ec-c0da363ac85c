#!/bin/bash

# Start Production Environment
echo "🚀 Starting SaaS User Auth - Production Environment"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it from .env.example"
    exit 1
fi

# Build all services
echo "🔨 Building all services..."
docker-compose build

# Start all services
echo "🚀 Starting production services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 15

# Check service health
echo "🔍 Checking service health..."
docker-compose ps

echo "✅ Production environment started!"
echo ""
echo "📊 Services:"
echo "   Application: http://localhost"
echo "   NGINX:       http://localhost:80"
echo ""
echo "🔍 Logs:"
echo "   docker-compose logs -f"
echo ""
echo "📈 Scale backend:"
echo "   docker-compose up -d --scale backend=3"
echo ""
echo "🛑 Stop:"
echo "   ./scripts/stop-prod.sh"
