# Yarn Workspace Guide

## Overview
This project uses Yarn workspaces for monorepo management. All packages are managed through the root `package.json`.

## Installation

### First Time Setup
```bash
# Install Yarn globally (if not already installed)
npm install -g yarn

# Install all dependencies
yarn install
```

## Workspace Structure
```
saas-userauth1/
├── apps/
│   ├── frontend/          # @saas-userauth/frontend
│   └── backend/           # @saas-userauth/backend
└── packages/
    ├── shared/            # @saas-userauth/shared
    └── ui/                # @saas-userauth/ui
```

## Common Commands

### Development
```bash
# Start both frontend and backend
yarn dev

# Start only frontend
yarn dev:frontend

# Start only backend
yarn dev:backend
```

### Building
```bash
# Build all packages
yarn build

# Build specific package
yarn build:frontend
yarn build:backend
```

### Database Operations
```bash
# Generate Prisma client
yarn db:generate

# Push schema to database
yarn db:push

# Run migrations
yarn db:migrate

# Open Prisma Studio
yarn db:studio
```

### Workspace-Specific Commands
```bash
# Run command in specific workspace
yarn workspace @saas-userauth/frontend dev
yarn workspace @saas-userauth/backend start
yarn workspace @saas-userauth/shared build

# Add dependency to specific workspace
yarn workspace @saas-userauth/frontend add react-query
yarn workspace @saas-userauth/backend add @nestjs/jwt

# Add dev dependency
yarn workspace @saas-userauth/frontend add -D @types/node
```

### Managing Dependencies

#### Add Dependencies
```bash
# Add to root (affects all workspaces)
yarn add -W typescript

# Add to specific workspace
yarn workspace @saas-userauth/frontend add next
yarn workspace @saas-userauth/backend add @nestjs/common
```

#### Remove Dependencies
```bash
# Remove from specific workspace
yarn workspace @saas-userauth/frontend remove lodash
yarn workspace @saas-userauth/backend remove old-package
```

### Cleaning and Maintenance
```bash
# Clean all node_modules and build artifacts
yarn clean

# Fresh install (clean + install)
yarn fresh

# Clean specific workspace
yarn workspace @saas-userauth/frontend clean
```

### Running Scripts Across Workspaces
```bash
# Run script in all workspaces that have it
yarn workspaces run build
yarn workspaces run test
yarn workspaces run lint

# Run script in specific workspace
yarn workspace @saas-userauth/frontend lint
yarn workspace @saas-userauth/backend test
```

## Troubleshooting

### Common Issues

1. **Dependency Resolution Issues**
   ```bash
   yarn fresh  # Clean install
   ```

2. **TypeScript Errors**
   ```bash
   yarn workspace @saas-userauth/frontend type-check
   yarn workspace @saas-userauth/backend build
   ```

3. **Database Connection Issues**
   ```bash
   yarn db:generate  # Regenerate Prisma client
   ```

### Useful Yarn Commands
```bash
# Check workspace info
yarn workspaces info

# List all workspaces
yarn workspaces list

# Check outdated packages
yarn outdated

# Upgrade dependencies
yarn upgrade-interactive
```

## Best Practices

1. **Always use workspace commands** for package-specific operations
2. **Install shared dependencies at root level** when possible
3. **Use `yarn fresh`** when switching branches or after major changes
4. **Run `yarn db:generate`** after schema changes
5. **Use `yarn workspaces run`** for bulk operations

## Environment Setup

1. Copy environment file:
   ```bash
   cp .env.example .env
   ```

2. Install dependencies:
   ```bash
   yarn install
   ```

3. Generate Prisma client:
   ```bash
   yarn db:generate
   ```

4. Start development:
   ```bash
   yarn dev
   ```
