'use client'

import React from 'react'
import { ThemeProvider } from 'next-themes'
import { ApolloWrapper } from '@/lib/apollo-wrapper'
import { AuthProvider } from '@/lib/auth-provider'
import { Toaster } from '@/components/ui/toaster'

interface ProvidersProps {
  children: React.ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <ApolloWrapper>
        <AuthProvider>
          {children}
          <Toaster />
        </AuthProvider>
      </ApolloWrapper>
    </ThemeProvider>
  )
}
