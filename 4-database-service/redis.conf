# Redis configuration for SaaS User Auth
# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# AOF
appendonly yes
appendfsync everysec

# Security
requirepass your_redis_password

# Network
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 300

# Logging
loglevel notice

# Performance
tcp-backlog 511
databases 16

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128
