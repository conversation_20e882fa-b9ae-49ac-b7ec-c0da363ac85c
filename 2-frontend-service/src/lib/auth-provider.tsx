"use client"

import { createContext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { gql, useApolloClient } from '@apollo/client'
import { type AuthResponse } from '@/types'

interface AuthContextType {
  user: AuthResponse['user'] | null
  loading: boolean
  signIn: (token: string, user: AuthResponse['user']) => void
  signOut: () => void
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signIn: () => {},
  signOut: () => {},
})

const ME_QUERY = gql`
  query Me {
    me {
      id
      email
      name
      role
    }
  }
`

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthResponse['user'] | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const client = useApolloClient()

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      client.query({
        query: ME_QUERY,
      }).then(({ data }) => {
        if (data?.me) {
          setUser(data.me)
        }
      }).catch(() => {
        localStorage.removeItem('token')
      }).finally(() => {
        setLoading(false)
      })
    } else {
      setLoading(false)
    }
  }, [client])

  const signIn = (token: string, user: AuthResponse['user']) => {
    localStorage.setItem('token', token)
    setUser(user)

    // Redirect based on user role
    if (user.role === 'ADMIN_USER') {
      router.push('/dashboard/admin')
    } else {
      router.push('/dashboard/user')
    }
  }

  const signOut = () => {
    localStorage.removeItem('token')
    setUser(null)
    client.resetStore()
    router.push('/auth/signin')
  }

  return (
    <AuthContext.Provider value={{ user, loading, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
