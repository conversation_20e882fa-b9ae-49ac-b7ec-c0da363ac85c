# 🔢 Numbered Microservices Structure

## Perfect Organization with Numbers

```
saas-userauth1/
├── 1-nginx-service/           # 🔄 Load Balancer & Proxy
│   ├── conf.d/               # NGINX configurations
│   ├── ssl/                  # SSL certificates
│   ├── Dockerfile            # Container build
│   └── nginx.conf            # Main configuration
│
├── 2-frontend-service/        # 🎨 User Interface Layer
│   ├── src/                  # Next.js 15 source code
│   ├── public/               # Static assets
│   ├── Dockerfile            # Production build
│   ├── Dockerfile.dev        # Development build
│   └── package.json          # Dependencies
│
├── 3-backend-service/         # ⚙️ Business Logic Layer
│   ├── src/                  # NestJS source code
│   ├── prisma/               # Database schema
│   ├── Dockerfile            # Production build
│   ├── Dockerfile.dev        # Development build
│   └── package.json          # Dependencies
│
├── 4-database-service/        # 🗄️ Data Persistence Layer
│   ├── init/                 # Database initialization
│   ├── backups/              # Automated backups
│   ├── docker-compose.yml    # PostgreSQL + Redis
│   └── redis.conf            # Redis configuration
│
├── scripts/                   # 🚀 Management Scripts
├── docs/                      # 📚 Documentation
├── docker-compose.yml         # 🐳 Production setup
└── docker-compose.dev.yml     # 🔧 Development setup
```

## 🎯 Service Flow (1 → 2 → 3 → 4)

### 1️⃣ NGINX Service (Entry Point)
- **Purpose**: Load balancer, SSL termination, static file serving
- **Port**: 80 (HTTP), 443 (HTTPS)
- **Routes**: 
  - `/` → Frontend Service
  - `/api/`, `/graphql` → Backend Service
- **Features**: Rate limiting, security headers, caching

### 2️⃣ Frontend Service (UI Layer)
- **Technology**: Next.js 15 with App Router
- **Port**: 3000 (internal)
- **Features**: 
  - Server-side rendering
  - Static generation
  - React components
  - Tailwind CSS + Shadcn/UI

### 3️⃣ Backend Service (API Layer)
- **Technology**: NestJS with GraphQL
- **Port**: 3001 (primary), 3002 (scaling)
- **Features**:
  - GraphQL API
  - Authentication & Authorization
  - Business logic
  - Database operations

### 4️⃣ Database Service (Data Layer)
- **PostgreSQL**: Primary database (Port: 5432)
- **Redis**: Cache & sessions (Port: 6379)
- **Backup**: Automated daily backups
- **Features**:
  - ACID compliance
  - Performance optimization
  - Data persistence

## 🚀 Quick Commands

### Development
```bash
# Start all services
./scripts/start-dev.sh

# Access services:
# Frontend:  http://localhost:3000
# Backend:   http://localhost:3001
# GraphQL:   http://localhost:3001/graphql
# Database:  localhost:5432
# Redis:     localhost:6379
```

### Production
```bash
# Start production environment
./scripts/start-prod.sh

# Access application:
# http://localhost (via NGINX)
```

### Individual Services
```bash
# 1. NGINX Service
cd 1-nginx-service && docker build -t nginx-service .

# 2. Frontend Service
cd 2-frontend-service && docker build -t frontend-service .

# 3. Backend Service
cd 3-backend-service && docker build -t backend-service .

# 4. Database Service
cd 4-database-service && docker-compose up -d
```

## 🔧 Database Service Details

### PostgreSQL Configuration
- **Image**: postgres:15-alpine
- **Database**: saas_userauth
- **User**: saas_user
- **Features**: 
  - Optimized for performance
  - Health checks
  - Automatic backups
  - Connection pooling

### Redis Configuration
- **Image**: redis:7-alpine
- **Features**:
  - Session storage
  - API caching
  - Rate limiting data
  - Persistence enabled

### Backup Service
- **Frequency**: Daily automated backups
- **Retention**: 7 days
- **Location**: `4-database-service/backups/`
- **Format**: SQL dumps with timestamps

## 🌟 Benefits of Numbered Structure

### 1. **Clear Service Order**
- Numbers indicate the request flow
- Easy to understand architecture
- Logical service dependencies

### 2. **Easy Navigation**
- Services are sorted naturally
- Quick identification in file explorer
- Consistent naming convention

### 3. **Deployment Order**
- Start services in numerical order
- Clear dependency chain
- Predictable startup sequence

### 4. **Team Organization**
- Different teams can own different numbers
- Clear service boundaries
- Independent development cycles

## 🎉 Ready for Development!

The numbered structure makes it crystal clear:
1. **NGINX** handles incoming requests
2. **Frontend** serves the user interface  
3. **Backend** processes business logic
4. **Database** stores and caches data

Each service is completely independent and can be developed, deployed, and scaled separately!
